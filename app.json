{"expo": {"name": "Emergent", "slug": "emergent-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo_build.png", "scheme": "emergentmobile", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"displayName": "Emergent AI", "supportsTablet": true, "bundleIdentifier": "com.emergentagent.app", "entitlements": {"aps-environment": "development"}, "infoPlist": {"CFBundleLocalizations": ["en"], "CFBundleDevelopmentRegion": "en"}, "usesAppleSignIn": true, "appExtensions": [{"targetName": "NotificationService", "bundleIdentifier": "com.emergentagent.app.richpush", "entitlements": {"com.apple.developer.usernotifications.service": true}}]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo_build.png", "backgroundColor": "#000"}, "edgeToEdgeEnabled": true, "package": "com.emergentagent.app"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logo_build.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#000"}], ["customerio-expo-plugin", {"android": {"googleServicesFile": "./files/google-services.json"}, "ios": {"pushNotification": {"useRichPush": true, "env": {"cdpApiKey": "cc4bfa2af7cf0b31b92d", "region": "us"}}}}], "expo-apple-authentication", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "a21a89c4-50d4-456e-8650-e39211874d11"}}}}