import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';

export const UserCredentialsDisplay: React.FC = () => {
  const { user, session, storedCredentials, signOut, refreshStoredCredentials } = useAuth();

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign Out', style: 'destructive', onPress: signOut },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const copyToClipboard = (text: string, label: string) => {
    // Note: In a real app, you'd use @react-native-clipboard/clipboard
    Alert.alert('Copied', `${label} copied to clipboard`);
    console.log(`${label}:`, text);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Credentials</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={refreshStoredCredentials}>
          <Text style={styles.refreshButtonText}>Refresh</Text>
        </TouchableOpacity>
      </View>

      {/* Current Session Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Session</Text>
        {user && (
          <View style={styles.infoContainer}>
            <Text style={styles.label}>User ID:</Text>
            <TouchableOpacity onPress={() => copyToClipboard(user.id, 'User ID')}>
              <Text style={styles.value}>{user.id}</Text>
            </TouchableOpacity>

            <Text style={styles.label}>Email:</Text>
            <TouchableOpacity onPress={() => copyToClipboard(user.email || '', 'Email')}>
              <Text style={styles.value}>{user.email}</Text>
            </TouchableOpacity>

            <Text style={styles.label}>Email Confirmed:</Text>
            <Text style={styles.value}>
              {user.email_confirmed_at ? formatDate(user.email_confirmed_at) : 'Not confirmed'}
            </Text>

            <Text style={styles.label}>Created At:</Text>
            <Text style={styles.value}>{formatDate(user.created_at)}</Text>

            <Text style={styles.label}>Last Updated:</Text>
            <Text style={styles.value}>{formatDate(user.updated_at)}</Text>
          </View>
        )}
      </View>

      {/* Session Tokens */}
      {session && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Session Tokens</Text>
          <View style={styles.infoContainer}>
            <Text style={styles.label}>Access Token:</Text>
            <TouchableOpacity onPress={() => copyToClipboard(session.access_token, 'Access Token')}>
              <Text style={styles.tokenValue} numberOfLines={3}>
                {session.access_token}
              </Text>
            </TouchableOpacity>

            <Text style={styles.label}>Refresh Token:</Text>
            <TouchableOpacity onPress={() => copyToClipboard(session.refresh_token || '', 'Refresh Token')}>
              <Text style={styles.tokenValue} numberOfLines={3}>
                {session.refresh_token}
              </Text>
            </TouchableOpacity>

            <Text style={styles.label}>Token Type:</Text>
            <Text style={styles.value}>{session.token_type}</Text>

            <Text style={styles.label}>Expires At:</Text>
            <Text style={styles.value}>
              {session.expires_at ? formatDate(new Date(session.expires_at * 1000).toISOString()) : 'N/A'}
            </Text>
          </View>
        </View>
      )}

      {/* Stored Credentials */}
      {storedCredentials && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Locally Stored Credentials</Text>
          <View style={styles.infoContainer}>
            <Text style={styles.label}>Stored At:</Text>
            <Text style={styles.value}>{formatDate(storedCredentials.stored_at)}</Text>

            <Text style={styles.label}>User Metadata:</Text>
            <Text style={styles.codeValue}>
              {JSON.stringify(storedCredentials.user.user_metadata, null, 2)}
            </Text>

            <Text style={styles.label}>App Metadata:</Text>
            <Text style={styles.codeValue}>
              {JSON.stringify(storedCredentials.user.app_metadata, null, 2)}
            </Text>
          </View>
        </View>
      )}

      <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
        <Text style={styles.signOutButtonText}>Sign Out</Text>
      </TouchableOpacity>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Tap on any value to copy it to clipboard (logged to console)
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  refreshButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  refreshButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    paddingBottom: 8,
  },
  infoContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 16,
  },
  label: {
    fontSize: 14,
    color: '#999',
    marginTop: 12,
    marginBottom: 4,
    fontWeight: '500',
  },
  value: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 8,
  },
  tokenValue: {
    fontSize: 12,
    color: '#007AFF',
    fontFamily: 'monospace',
    marginBottom: 8,
  },
  codeValue: {
    fontSize: 12,
    color: '#00ff00',
    fontFamily: 'monospace',
    backgroundColor: '#0a0a0a',
    padding: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  signOutButton: {
    backgroundColor: '#ff3b30',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  signOutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
