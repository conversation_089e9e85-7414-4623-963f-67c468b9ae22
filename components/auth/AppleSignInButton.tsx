import React from 'react';
import { Platform, View, TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import * as AppleAuthentication from 'expo-apple-authentication';

interface AppleSignInButtonProps {
  onPress: () => Promise<void>;
  loading?: boolean;
}

export const AppleSignInButton: React.FC<AppleSignInButtonProps> = ({ onPress, loading = false }) => {
  if (Platform.OS === 'ios') {
    return (
      <AppleAuthentication.AppleAuthenticationButton
        buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
        buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
        cornerRadius={8}
        style={styles.appleButton}
        onPress={onPress}
      />
    );
  }

  // Fallback for non-iOS platforms
  return (
    <TouchableOpacity
      style={[styles.button, styles.appleButtonFallback]}
      onPress={onPress}
      disabled={loading}
    >
      {loading ? (
        <ActivityIndicator color="#fff" />
      ) : (
        <Text style={styles.buttonText}>Continue with Apple</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  appleButton: {
    width: '100%',
    height: 50,
  },
  button: {
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
    height: 50,
    justifyContent: 'center',
  },
  appleButtonFallback: {
    backgroundColor: '#000',
    borderWidth: 1,
    borderColor: '#333',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
