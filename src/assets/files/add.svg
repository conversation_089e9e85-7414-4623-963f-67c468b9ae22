<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_11428_89821)">
<g filter="url(#filter0_d_11428_89821)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 1C7.92955 1.00001 8.27777 1.34822 8.27778 1.77778L8.27777 7.22221L13.7222 7.22218C14.1518 7.22222 14.5 7.57041 14.5 8C14.5 8.42955 14.1518 8.77776 13.7222 8.77778L8.27777 8.77775L8.27783 14.2222C8.27777 14.6518 7.92958 14.9999 7.5 15C7.07042 14.9999 6.72223 14.6518 6.72218 14.2222L6.72224 8.77775L1.27778 8.77778C0.848235 8.77776 0.500016 8.42954 0.5 8C0.500044 7.5704 0.848231 7.22222 1.27782 7.22217L6.72224 7.22221L6.72222 1.77777C6.72222 1.34822 7.07044 1.00001 7.5 1Z" fill="#80FFF9"/>
</g>
</g>
<defs>
<filter id="filter0_d_11428_89821" x="-39.5" y="-39" width="94" height="94" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="20"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.501961 0 0 0 0 1 0 0 0 0 0.976471 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11428_89821"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11428_89821" result="shape"/>
</filter>
<clipPath id="clip0_11428_89821">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
