<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_10889_53591)">
<g filter="url(#filter0_ii_10889_53591)">
<path d="M47.04 0H16.96C7.59325 0 0 7.59325 0 16.96V47.04C0 56.4067 7.59325 64 16.96 64H47.04C56.4067 64 64 56.4067 64 47.04V16.96C64 7.59325 56.4067 0 47.04 0Z" fill="#0B0C0D"/>
</g>
<g filter="url(#filter1_dddddiii_10889_53591)">
<path d="M46.481 3.19922H17.5253C9.61538 3.19922 3.20312 9.61147 3.20312 17.5214V46.4771C3.20312 54.387 9.61538 60.7992 17.5253 60.7992H46.481C54.3909 60.7992 60.8031 54.387 60.8031 46.4771V17.5214C60.8031 9.61147 54.3909 3.19922 46.481 3.19922Z" fill="url(#paint0_linear_10889_53591)" fill-opacity="0.7"/>
<path d="M46.481 3.19922H17.5253C9.61538 3.19922 3.20312 9.61147 3.20312 17.5214V46.4771C3.20312 54.387 9.61538 60.7992 17.5253 60.7992H46.481C54.3909 60.7992 60.8031 54.387 60.8031 46.4771V17.5214C60.8031 9.61147 54.3909 3.19922 46.481 3.19922Z" fill="url(#paint1_linear_10889_53591)" fill-opacity="0.2"/>
</g>
<g filter="url(#filter2_dddddi_10889_53591)">
<path d="M32.0001 16C23.1646 16 16 23.3447 16 32.405C16 39.6533 20.5845 45.8026 26.9419 47.9718C27.7415 48.1236 28.0351 47.6159 28.0351 47.1826C28.0351 46.7915 28.0202 45.4991 28.0134 44.1283C23.562 45.1207 22.6228 42.1927 22.6228 42.1927C21.8949 40.2965 20.8463 39.7924 20.8463 39.7924C19.3946 38.7742 20.9557 38.795 20.9557 38.795C22.5624 38.9108 23.4084 40.4856 23.4084 40.4856C24.8355 42.9936 27.1514 42.2685 28.0645 41.8495C28.208 40.789 28.6227 40.0654 29.0803 39.6558C25.5265 39.2408 21.7905 37.8342 21.7905 31.5483C21.7905 29.7573 22.4156 28.2938 23.4392 27.1449C23.273 26.7317 22.7254 25.0632 23.5941 22.8035C23.5941 22.8035 24.9377 22.3626 27.9953 24.4851C29.2715 24.1215 30.6403 23.9393 32.0001 23.9332C33.3598 23.9393 34.7296 24.1215 36.0083 24.4851C39.0623 22.3626 40.404 22.8035 40.404 22.8035C41.2748 25.0632 40.727 26.7317 40.5608 27.1449C41.5867 28.2938 42.2075 29.7572 42.2075 31.5483C42.2075 37.8491 38.4645 39.2366 34.9017 39.6426C35.4755 40.1518 35.9869 41.1502 35.9869 42.6806C35.9869 44.8756 35.9683 46.6422 35.9683 47.1826C35.9683 47.6192 36.2563 48.1307 37.0674 47.9696C43.4213 45.798 48 39.6509 48 32.405C48 23.3447 40.8364 16 32.0001 16Z" fill="#2EE572"/>
</g>
</g>
<defs>
<filter id="filter0_ii_10889_53591" x="0" y="-0.21728" width="64" height="64.4327" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.21728"/>
<feGaussianBlur stdDeviation="0.1088"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.180392 0 0 0 0 0.898039 0 0 0 0 0.447059 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.215385"/>
<feGaussianBlur stdDeviation="0.1088"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.180392 0 0 0 0 0.898039 0 0 0 0 0.447059 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_10889_53591" result="effect2_innerShadow_10889_53591"/>
</filter>
<filter id="filter1_dddddiii_10889_53591" x="-0.300824" y="2.5211" width="64.6095" height="74.1035" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.452082"/>
<feGaussianBlur stdDeviation="0.565103"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.36 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.92135"/>
<feGaussianBlur stdDeviation="0.96083"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_10889_53591" result="effect2_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.40811"/>
<feGaussianBlur stdDeviation="1.35625"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_10889_53591" result="effect3_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.91206"/>
<feGaussianBlur stdDeviation="1.58229"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_10889_53591" result="effect4_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12.3199"/>
<feGaussianBlur stdDeviation="1.75197"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_10889_53591" result="effect5_dropShadow_10889_53591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_10889_53591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.791144"/>
<feGaussianBlur stdDeviation="0.791144"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="shape" result="effect6_innerShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.678123"/>
<feGaussianBlur stdDeviation="0.226041"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.02 0"/>
<feBlend mode="normal" in2="effect6_innerShadow_10889_53591" result="effect7_innerShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.113021"/>
<feGaussianBlur stdDeviation="0.0566659"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect7_innerShadow_10889_53591" result="effect8_innerShadow_10889_53591"/>
</filter>
<filter id="filter2_dddddi_10889_53591" x="-4.08033" y="12.708" width="72.1607" height="127.136" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.96281"/>
<feGaussianBlur stdDeviation="3.12739"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.91 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="11.5217"/>
<feGaussianBlur stdDeviation="5.7607"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.79 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_10889_53591" result="effect2_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="25.6768"/>
<feGaussianBlur stdDeviation="7.73602"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.47 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_10889_53591" result="effect3_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="46.0866"/>
<feGaussianBlur stdDeviation="9.21726"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.14 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_10889_53591" result="effect4_dropShadow_10889_53591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="71.7634"/>
<feGaussianBlur stdDeviation="10.0402"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_10889_53591" result="effect5_dropShadow_10889_53591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_10889_53591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.22694"/>
<feGaussianBlur stdDeviation="0.613472"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect6_innerShadow_10889_53591"/>
</filter>
<linearGradient id="paint0_linear_10889_53591" x1="32.0031" y1="3.19922" x2="32.0031" y2="60.7992" gradientUnits="userSpaceOnUse">
<stop stop-color="#242528"/>
<stop offset="1" stop-color="#111113"/>
</linearGradient>
<linearGradient id="paint1_linear_10889_53591" x1="32.0031" y1="52.8878" x2="32.0031" y2="60.7992" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0"/>
<stop offset="1"/>
</linearGradient>
<clipPath id="clip0_10889_53591">
<rect width="64" height="64" fill="white"/>
</clipPath>
</defs>
</svg>
