<svg width="176" height="88" viewBox="0 0 176 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_10768_91486)">
<path d="M87.1903 42.5156C87.647 44.2901 89.2577 45.6015 91.1747 45.6016C93.0918 45.6016 94.7024 44.2901 95.1591 42.5156H97.6054C97.1118 45.6237 94.4208 47.9998 91.1737 48C87.9265 48 85.2348 45.6239 84.7411 42.5156H87.1903ZM97.6835 34.5928C97.6836 34.6055 97.6845 34.6182 97.6845 34.6309C97.6845 34.6432 97.6835 34.6556 97.6835 34.668V34.9727H97.6747C97.4969 38.4113 94.6528 41.1445 91.1698 41.1445C87.5724 41.1443 84.6561 38.2282 84.6561 34.6309C84.6562 31.0336 87.5724 28.1174 91.1698 28.1172C92.7302 28.1172 94.1622 28.6662 95.2841 29.5811V27.4297H97.6835V34.5928ZM117.912 30.1719C118.532 29.1845 119.733 28.8008 121.341 28.8008C123.959 28.8008 125.797 31.0959 125.797 33.6455V41.1445H123.368V34.1973C123.368 32.3597 122.216 31.2002 120.654 31.2002C119.069 31.2003 117.912 32.4516 117.912 34.1973V41.1445H115.512V28.8008H117.912V30.1719ZM131.277 27.4287H134.019V29.8281H131.277V38.7451H135.738V41.1445H128.882V41.1416H128.877V29.8281H127.163V27.4287H128.877V24H131.277V27.4287ZM43.1698 30.1719C43.8555 29.1437 45.1567 28.8008 46.5975 28.8008C48.0386 28.8008 49.3405 29.4863 50.0263 30.5146C50.7119 29.4863 51.7408 28.8008 53.7977 28.8008C56.7118 28.8009 58.9403 30.6866 58.9403 33.2578V41.1426H56.5399V33.9434C56.5399 32.1537 55.355 31.0294 53.7977 31.0293C52.1939 31.0293 51.0546 32.2466 51.0546 33.9434V41.1426H48.6552V33.9434C48.6552 32.1538 47.3215 31.0295 45.7411 31.0293C44.1373 31.0293 43.1698 32.1509 43.1698 33.8477V41.1426H40.7694V28.8008H43.1698V30.1719ZM74.7079 33.9961C74.8184 36.6358 73.3888 39.216 70.8622 40.4482C68.3356 41.6805 65.4222 41.2182 63.41 39.5059L65.9022 38.291C67.0925 38.8709 68.5282 38.9164 69.8104 38.291C71.0927 37.6655 71.94 36.506 72.2157 35.2109L74.7079 33.9961ZM113.45 33.9961C113.561 36.6358 112.131 39.216 109.604 40.4482C107.078 41.6805 104.164 41.2182 102.152 39.5059L104.644 38.291C105.835 38.8709 107.27 38.9164 108.553 38.291C109.835 37.6655 110.682 36.506 110.958 35.2109L113.45 33.9961ZM83.9638 30.1699L79.1639 32.5693V41.1406H76.7636V28.1133H79.1639V30.1699L83.9638 27.7695V30.1699ZM38.7079 33.9922C38.8184 36.6319 37.3888 39.2121 34.8622 40.4443C32.3356 41.6766 29.4222 41.2143 27.41 39.502L29.9022 38.2871C31.0925 38.867 32.5282 38.9125 33.8104 38.2871C35.0927 37.6616 35.94 36.5021 36.2157 35.207L38.7079 33.9922ZM91.1688 30.5156C88.8966 30.5156 87.0546 32.3578 87.0546 34.6299C87.0547 36.9019 88.8967 38.7441 91.1688 38.7441C93.4409 38.7441 95.283 36.9018 95.2831 34.6299C95.2831 32.3578 93.441 30.5157 91.1688 30.5156ZM64.8514 28.125C68.2544 26.4653 72.3587 27.8784 74.0184 31.2812C74.1199 31.4892 74.2102 31.6996 74.2889 31.9121L62.0263 37.8926C61.9073 37.6998 61.7966 37.4989 61.6952 37.291C60.0358 33.8883 61.4487 29.7846 64.8514 28.125ZM103.594 28.125C106.997 26.4653 111.101 27.8784 112.761 31.2812C112.862 31.4892 112.952 31.6996 113.031 31.9121L100.768 37.8926C100.65 37.6998 100.539 37.4989 100.437 37.291C98.778 33.8883 100.191 29.7846 103.594 28.125ZM28.8514 28.1211C32.2544 26.4614 36.3587 27.8745 38.0184 31.2773C38.1199 31.4853 38.2102 31.6957 38.2889 31.9082L26.0263 37.8887C25.9073 37.6959 25.7966 37.495 25.6952 37.2871C24.0358 33.8844 25.4487 29.7807 28.8514 28.1211ZM91.2108 34.9736H91.1366C91.149 34.9736 91.1613 34.9727 91.1737 34.9727C91.1861 34.9727 91.1985 34.9736 91.2108 34.9736ZM70.8036 30.9424C69.4941 29.7895 67.5693 29.4687 65.9032 30.2812C64.2372 31.0938 63.3048 32.8082 63.4071 34.5498L70.8036 30.9424ZM109.546 30.9424C108.236 29.7895 106.311 29.4687 104.645 30.2812C102.979 31.0938 102.047 32.8082 102.149 34.5498L109.546 30.9424ZM34.8036 30.9385C33.4941 29.7856 31.5693 29.4648 29.9032 30.2773C28.2372 31.0899 27.3048 32.8043 27.4071 34.5459L34.8036 30.9385Z" fill="#80FFF9"/>
</g>
<defs>
<filter id="filter0_d_10768_91486" x="-15" y="-16" width="190.738" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="20"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.501961 0 0 0 0 1 0 0 0 0 0.976471 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10768_91486"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10768_91486" result="shape"/>
</filter>
</defs>
</svg>
