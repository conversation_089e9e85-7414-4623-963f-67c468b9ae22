import { useState, useEffect, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import PlaySVG from "@/assets/preview/play.svg";
import DinoDash from "@/assets/preview/dino-dash-banner.png";
import BattleStar from "@/assets/preview/battlestar.png";
import FlappyBird from "@/assets/preview/flappybird.png";
import TetrisSmash from "@/assets/preview/tetrissmash.png";
import NextArrow from "@/assets/tips/arrow-back.svg";
import { useArrowNavigation } from "./hooks/useArrowNavigation";

const GameBoyCarousel = ({
  currentView,
  setCurrentView,
  setLoadUrl,
}: {
  currentView: "game" | "play";
  setCurrentView: (view: "game" | "play") => void;
  setLoadUrl: (url: string) => void;
}) => {
  const games = [
    {
      id: 1,
      title: "Flappy Bird",
      color: "bg-yellow-500",
      img: FlappyBird,
      link: "https://flapppy.netlify.app/",
    },
    // {
    //   id: 2,
    //   title: "Battle Star",
    //   color: "bg-purple-500",
    //   img: BattleStar,
    //   link: "https://code-explorer-6.emergent.host/",
    // },
    // {
    //   id: 5,
    //   title: "Dino Dash",
    //   color: "bg-green-500",
    //   img: DinoDash,
    //   link: "https://chrome-dino-game.github.io/",
    // },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const currentIndexRef = useRef(0);
  const isHoveredRef = useRef(false);

  const AUTO_ADVANCE_DURATION = 2500; // 2.5 seconds per slide
  const PROGRESS_UPDATE_INTERVAL = 50; // Update progress every 50ms

  // Update refs when state changes
  useEffect(() => {
    currentIndexRef.current = currentIndex;
  }, [currentIndex]);

  useEffect(() => {
    isHoveredRef.current = isHovered;
  }, [isHovered]);

  const nextSlide = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prev) => (prev + 1) % games.length);
    setTimeout(() => setIsTransitioning(false), 500);
  }, [isTransitioning, games.length]);

  const prevSlide = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prev) => (prev - 1 + games.length) % games.length);
    setTimeout(() => setIsTransitioning(false), 500);
  }, [isTransitioning, games.length]);

  // Stable auto-advance functionality that doesn't depend on currentIndex
  useEffect(() => {
    const startAutoAdvance = () => {
      // Clear existing intervals
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (progressIntervalRef.current)
        clearInterval(progressIntervalRef.current);

      if (isHoveredRef.current) return;

      // Reset progress
      setProgress(0);

      // Start auto-advance timer
      intervalRef.current = setInterval(() => {
        if (!isHoveredRef.current) {
          nextSlide();
        }
      }, AUTO_ADVANCE_DURATION);

      // Start progress animation
      const progressStep =
        (PROGRESS_UPDATE_INTERVAL / AUTO_ADVANCE_DURATION) * 100;
      progressIntervalRef.current = setInterval(() => {
        if (!isHoveredRef.current) {
          setProgress((prev) => {
            if (prev >= 100) return 100;
            return prev + progressStep;
          });
        }
      }, PROGRESS_UPDATE_INTERVAL);
    };

    startAutoAdvance();

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (progressIntervalRef.current)
        clearInterval(progressIntervalRef.current);
    };
  }, [nextSlide]); // Only depend on nextSlide, not on currentIndex or isHovered

  // Handle hover state changes
  useEffect(() => {
    if (isHovered) {
      // Pause progress when hovered
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    } else {
      // Resume progress when not hovered
      if (!progressIntervalRef.current) {
        const progressStep =
          (PROGRESS_UPDATE_INTERVAL / AUTO_ADVANCE_DURATION) * 100;
        progressIntervalRef.current = setInterval(() => {
          if (!isHoveredRef.current) {
            setProgress((prev) => {
              if (prev >= 100) return 100;
              return prev + progressStep;
            });
          }
        }, PROGRESS_UPDATE_INTERVAL);
      }
    }
  }, [isHovered]);

  // Reset progress when currentIndex changes (separate effect)
  useEffect(() => {
    setProgress(0);
  }, [currentIndex]);

  // Use the extracted arrow navigation hook
  useArrowNavigation({
    onNext: nextSlide,
    onPrevious: prevSlide,
  });

  const activeGame = games[currentIndex];

  // Get item style based on the smooth carousel logic
  const getItemStyle = (index: number) => {
    const diff = index - currentIndex;
    const totalItems = games.length;

    // Handle wrapping for infinite carousel
    let position = diff;
    if (diff > totalItems / 2) position -= totalItems;
    if (diff < -totalItems / 2) position += totalItems;

    const isMobile = window.innerWidth < 768;

    const size = isMobile ? 250 : 350;
    const heightMobile = isMobile ? 100 : 170;
    const inActiveWidth = isMobile ? 200 : 280;

    // Calculate styles based on position
    const baseTranslate = position * size; // 350px apart
    const scale = position === 0 ? 1 : 0.9;
    const opacity = position === 0 ? 1 : Math.abs(position) === 1 ? 0.6 : 0;
    const zIndex = position === 0 ? 20 : Math.abs(position) === 1 ? 10 : 0;

    // Size based on position (will animate smoothly)
    const width = position === 0 ? size : inActiveWidth;
    const height = position === 0 ? 200 : heightMobile;

    return {
      transform: `translateX(${baseTranslate}px) scale(${scale})`,
      opacity: opacity,
      zIndex: zIndex,
      width: `${width}px`,
      height: `${height}px`,
      transition: "all 0.5s ease-out",
    };
  };

  const handleManualNavigation = (index: number) => {
    if (!isTransitioning && index !== currentIndex) {
      setIsTransitioning(true);
      setCurrentIndex(index);
      setTimeout(() => setIsTransitioning(false), 500);
    }
  };

  const handlePlayClick = async () => {
    setCurrentView("play");
  };

  return (
    <div className="flex flex-col items-center justify-between w-full overflow-hidden">
      <div className="flex flex-col justify-center w-full h-full max-w-4xl ">
        <div>
          {/* Game Cards Carousel */}
          <div className="relative flex items-center justify-center md:mb-8 h-[500px] md:h-[500px]">
            {/* All items positioned absolutely from center */}
            <div className="relative flex items-center justify-center w-full h-full">
              {games.map((game, index) => {
                const style = getItemStyle(index);
                const diff = index - currentIndex;
                const totalItems = games.length;
                let position = diff;
                if (diff > totalItems / 2) position -= totalItems;
                if (diff < -totalItems / 2) position += totalItems;
                const isCenter = position === 0;

                return (
                  <div
                    key={game.id}
                    className="absolute select-none rounded-[10px] w-[250px] md:w-[350px]  flex items-center justify-center cursor-pointer"
                    style={style}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                    onClick={() => handleManualNavigation(index)}
                  >
                    <img
                      src={game.img}
                      alt={game.title}
                      className="w-[250px] select-none md:w-[350px] "
                    />

                    {/* Overlay for non-center items */}
                    {!isCenter && (
                      <div className="absolute inset-0 transition-colors duration-200 " />
                    )}

                    {/* Subtle glow effect for center item */}
                    {isCenter && (
                      <div className="absolute inset-0 rounded-[10px] pointer-events-none" />
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Play Button */}
          <div className="flex justify-center">
            <button
              type="button"
              onClick={async () => {
                await handlePlayClick();
                setLoadUrl(activeGame.link);
              }}
              className="flex items-center px-6 py-3 gap-2 font-semibold text-black transition-all duration-300 rounded-[10px] shadow-lg hover:bg-gray-100 bg-white white_glow"
            >
              <img src={PlaySVG} alt="play" className="w-5 h-5" />
              <span className="text-[16px] font-semibold leading-[24px]">
                Play {activeGame.title}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Navigation and dots */}
      <div className="flex flex-col items-center justify-center gap-6 mt-4 px-[40px]">
        <div className="flex items-center justify-center mt-4 space-x-4">
          <button
            type="button"
            onClick={prevSlide}
            className={cn(
              "h-8 w-8 hover:bg-[#3A3A3B] backdrop-blur-lg bg-[#FFFFFF10] rounded-full transition-all duration-200 flex items-center justify-center",
              isTransitioning
                ? "opacity-25 cursor-not-allowed"
                : "opacity-50 hover:opacity-100"
            )}
            aria-label="Previous game"
          >
            <img src={NextArrow} alt="Previous" className="w-4 h-4" />
          </button>

          {/* Progress indicators */}
          <div className="flex space-x-2">
            {games.map((_, index) => (
              <motion.button
                key={index}
                type="button"
                onClick={() => handleManualNavigation(index)}
                className="relative h-2 rounded-full bg-[#FFFFFF]/10 overflow-hidden"
                aria-label={`Go to game ${index + 1}`}
                animate={{
                  width: index === currentIndex ? 50 : 8,
                }}
                transition={{
                  duration: 0.3,
                  ease: "easeInOut",
                }}
              >
                {/* Background fill for completed slides */}
                <motion.div
                  className="absolute inset-0 rounded-full bg-[#FFFFFF]/20"
                  initial={{ scaleX: 0 }}
                  animate={{
                    scaleX: index < currentIndex ? 1 : 0,
                  }}
                  transition={{
                    duration: 0.3,
                    ease: "easeOut",
                  }}
                />

                {/* Active slide progress */}
                {index === currentIndex && (
                  <motion.div
                    className="absolute inset-0 origin-left bg-white rounded-full"
                    key={`progress-${currentIndex}`} // Force re-mount on index change
                    initial={{ scaleX: 0 }}
                    animate={{
                      scaleX: progress / 100,
                    }}
                    transition={{
                      duration: 0.1,
                      ease: "linear",
                    }}
                  />
                )}
              </motion.button>
            ))}
          </div>

          <button
            type="button"
            onClick={nextSlide}
            className={cn(
              "h-8 w-8 bg-[#FFFFFF10] backdrop-blur-lg hover:bg-[#3A3A3B] rounded-full transition-all duration-200 flex items-center justify-center",
              isTransitioning
                ? "opacity-25 cursor-not-allowed"
                : "opacity-50 hover:opacity-100"
            )}
            aria-label="Next game"
          >
            <img src={NextArrow} alt="Next" className="w-4 h-4 rotate-180" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameBoyCarousel;
