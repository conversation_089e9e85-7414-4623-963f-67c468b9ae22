import { useState, useEffect } from "react";
import Logo from "@/assets/preview/emergent_blue.svg";
// @ts-ignore
import MatrixCode from "../../assets/fork/matrix_code.gif";
import { BoxLoader } from "@/components/ui/box-loader";
import { Mail, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ScreenImage from "../../assets/preview/screen.svg";
import GameBoy from "@/assets/preview/game_boy.svg";
import GameBoyCarousel from "./GameBoyCarousel";

import PixelArrow from "@/assets/preview/pixel_arrow.svg";
import GreenArrow from "@/assets/preview/green_arrow.svg";
import <PERSON><PERSON>heck from "@/assets/preview/green_check.svg";
import GradientArrow from "@/assets/preview/gradient-arrow.svg";
import axios from "axios";

import { Sheet, SheetClose, SheetContent } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";

function reloadIframe() {
  // Send message to parent window
  window.parent.postMessage(
    {
      type: "reload",
    },
    "*",
  );
}

function openURLIframe(url: string): void {
  // Check if we are inside an iframe by comparing window.self with window.top
  const isInIframe = window.self !== window.top;

  if (isInIframe) {
    // Send message to parent window if we're in an iframe
    window.parent.postMessage(
      {
        type: "url",
        url,
      },
      "*",
    );
  } else {
    // If we're not in an iframe, open the URL directly
    window.open(url, "_blank");
  }
}

export default function PreviewLoading() {
  const [currentView, setCurrentView] = useState<"game" | "play">("game");
  const isMobile = window.innerWidth < 768;

  // Get the host parameter from URL and log it
  const urlParams = new URLSearchParams(window.location.search);
  const hostParam = urlParams.get("host");

  const [sheetOpen, setSheetOpen] = useState(false);
  const [loadUrl, setLoadUrl] = useState("");

  const [progress, setProgress] = useState(0);
  const [loadingState, setLoadingState] = useState<"loading" | "completed">(
    "loading",
  );

  // Poll the host every 2 seconds
  useEffect(() => {
    if (!hostParam) return;

    const pollHost = async () => {
      try {
        const response = await axios.get(`https://${hostParam}`);
        console.log(response, "response");
        if (response.status === 200) {
          console.error("Host is ready with status 200");
          setLoadingState("completed");
          return true; // Successfully reached the host
        }
        return false;
      } catch (error) {
        console.error("Error polling host:", error);
        return false;
      }
    };

    const intervalId = setInterval(async () => {
      const success = await pollHost();
      if (success) {
        clearInterval(intervalId); // Stop polling once we get a 200
      }
    }, 2000);

    // Clean up interval on component unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [hostParam]);

  // Random progress over 2 minutes (120 seconds)
  useEffect(() => {
    const startTime = Date.now();
    const duration = 20 * 60 * 1000; // 2 minutes in milliseconds
    let currentProgress = 0;

    const updateProgress = () => {
      const elapsed = Date.now() - startTime;
      const timeProgress = (elapsed / duration) * 100; // Linear time progress

      // Add randomness but ensure we don't go backwards and reach 100% by the end
      const randomIncrement = Math.random() * 3 + 0.5; // Random increment between 0.5 and 3.5

      // Ensure progress only moves forward (never backwards)
      const newProgress = Math.min(
        currentProgress + randomIncrement, // Always move forward
        Math.max(timeProgress * 1.2, currentProgress + randomIncrement), // Allow some variance but stay roughly on track
      );

      currentProgress = Math.min(newProgress, 100);
      setProgress(currentProgress);

      // Check if we've completed
      if (currentProgress >= 100 || elapsed >= duration) {
        setProgress(100);
        // setLoadingState("completed");
        return;
      }

      // Continue updating
      if (loadingState === "loading") {
        // Random delay between updates (100ms to 800ms)
        const randomDelay = Math.random() * 700 + 100;
        setTimeout(() => requestAnimationFrame(updateProgress), randomDelay);
      }
    };

    // Start the progress animation
    updateProgress();
  }, [loadingState]);

  return (
    <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
      <div className="flex relative items-center justify-center min-h-screen w-full bg-[#090D0F]">
        <div className="absolute top-0 bottom-0 left-0 grid w-full h-full grid-cols-3 pointer-events-none z-[1] opacity-[0.2] overflow-hidden">
          <img
            src={MatrixCode}
            alt="Matrix Code"
            className="object-cover w-full h-full "
            style={{
              filter: "sepia(1) hue-rotate(180deg) saturate(2) brightness(0.5)",
            }}
          />
          <img
            src={MatrixCode}
            alt="Matrix Code"
            className="object-cover w-full h-full"
            style={{
              filter: "sepia(1) hue-rotate(180deg) saturate(2) brightness(0.5)",
            }}
          />
          <img
            src={MatrixCode}
            alt="Matrix Code"
            className="object-cover w-full h-full "
            style={{
              filter: "sepia(1) hue-rotate(180deg) saturate(2) brightness(0.5)",
            }}
          />
          <img
            src={MatrixCode}
            alt="Matrix Code"
            className="object-cover w-full h-full"
            style={{
              filter: "sepia(1) hue-rotate(180deg) saturate(2) brightness(0.5)",
            }}
          />
          <img
            src={MatrixCode}
            alt="Matrix Code"
            className="object-cover w-full h-full "
            style={{
              filter: "sepia(1) hue-rotate(180deg) saturate(2) brightness(0.5)",
            }}
          />
          <img
            src={MatrixCode}
            alt="Matrix Code"
            className="object-cover w-full h-full"
            style={{
              filter: "sepia(1) hue-rotate(180deg) saturate(2) brightness(0.5)",
            }}
          />
        </div>
        <img
          alt="Emergent Logo"
          src={Logo}
          className="absolute h-[24px] scale-[3.5] top-[40px] left-[60px] md:hidden"
        />
        <div className="w-full flex flex-col justify-between flex-1 h-screen p-6 md:p-12 relative z-[2]">
          <div className="h-[200px]"></div>
          <div
            className={cn(
              "flex flex-col w-full h-full gap-4",
              loadingState === "completed" ? "gap-6" : "gap-10",
            )}
          >
            <div className="flex flex-col gap-6">
              <img
                alt="Emergent Screen"
                src={loadingState === "completed" ? GreenCheck : ScreenImage}
                className="w-10 h-10"
              />
              <div
                className={cn(
                  "flex flex-col ",
                  loadingState === "completed" ? "gap-2" : "gap-6"
                )}
              >
                <p
                  className=" text-[24px] md:text-[32px] font-semibold text-white"
                  style={{ textShadow: "0px 0px 20px #FFFFFF66" }}
                >
                  {loadingState === "completed"
                    ? "Your Preview is Ready!"
                    : "Booting up the preview..."}
                </p>

                {loadingState === "loading" && (
                  <BoxLoader
                    progress={progress / 100}
                    boxClassname="w-full w-[36px] h-6 p-1 flex items-center justify-center "
                  />
                )}

                {loadingState == "completed" && (
                  <p className="font-medium font-jetbrains text-sm/6 text-white/40">
                    Your pod has successfully booted up and is ready to explore.
                  </p>
                )}
              </div>
            </div>

            {loadingState !== "completed" && (
              <p className="font-medium font-jetbrains text-sm/6 text-white/40">
                This usually takes around{" "}
                <span className="text-white">3-5 mins</span>, <br />
                please enjoy these amazing vibe-coded games from our community.
              </p>
            )}
            {loadingState != "completed" && !isMobile && (
              <div className="flex items-center space-x-4 max-md:hidden">
                <Button
                  className=" h-[44px] w-[186px] px-4 py-3 flex space-x-2 justify-center items-center gap-4 text-white text-base font-medium rounded-[10px] backdrop-blur-[25px]"
                  style={{
                    background:
                      "radial-gradient(50% 50% at 50% 50%, rgba(252, 185, 73, 0.15) 0%, rgba(252, 185, 73, 0.08) 100%)",
                  }}
                  onClick={() => openURLIframe("https://app.emergent.sh")}
                >
                  <div
                    className="flex items-center gap-2 text-transparent bg-clip-text"
                    style={{
                      background:
                        "linear-gradient(270deg, #FCB949 40.48%, #E28C37 92.65%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                    }}
                  >
                    <p className="font-medium">Open Emergent </p>
                    <img
                      src={GradientArrow}
                      alt="Right Arrow"
                      className="w-6 h-6"
                    />
                  </div>
                </Button>
                <Button
                  className=" h-[44px] w-[153px] px-4 py-3 flex space-x-2 justify-center items-center gap-4 text-white text-base font-medium rounded-[10px] backdrop-blur-[25px]"
                  style={{
                    background:
                      "radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)",
                  }}
                  onClick={() => openURLIframe("https://help.emergent.sh")}
                >
                  <div className="flex items-center gap-2">
                    <p className="font-medium">Need Help? </p>
                    <Mail color="white" strokeWidth={3} size={24} />
                  </div>
                </Button>
              </div>
            )}

            <div>
              {loadingState === "loading" && (
                <button
                  onClick={() => setSheetOpen(true)}
                  className=" md:hidden flex items-center gap-2 h-[44px] px-4 py-[10px] font-semibold justify-center text-black bg-white text-base rounded-[10px] white_glow"
                >
                  <img src={GameBoy} alt="Right Arrow" className="w-6 h-6" />
                  <p className="p-0 font-medium">Explore Games </p>
                </button>
              )}

              {loadingState === "completed" && (
                <div className="flex gap-3">
                  <button
                    onClick={reloadIframe}
                    className="max-md:w-full  p-[10px] px-[20px] md:gap-2 items-center green-radial rounded-[10px] flex justify-between"
                    title="Launch"
                  >
                    <span className="text-[#5FE55C] font-medium">
                      Launch App
                    </span>
                    <img src={GreenArrow} alt="play" className="w-6 h-6" />
                  </button>

                  <button
                    onClick={() => {
                      setSheetOpen(true);
                    }}
                    className=" md:hidden p-3 px-6 bg-white rounded-[10px] flex justify-between"
                    title="Launch"
                  >
                    <img src={GameBoy} alt="play" className="w-6 h-6" />
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-end justify-start max-md:hidden">
            <img
              alt="Emergent Logo"
              src={Logo}
              className="h-6 scale-[4] ml-12 mt-8"
            />
          </div>

          <div className="flex items-center space-x-4 md:hidden">
            <Button
              className=" h-[44px] w-[186px] px-4 py-3 flex space-x-2 justify-center items-center gap-4 text-white text-base font-medium rounded-[10px] backdrop-blur-[25px]"
              style={{
                background:
                  "radial-gradient(50% 50% at 50% 50%, rgba(252, 185, 73, 0.15) 0%, rgba(252, 185, 73, 0.08) 100%)",
              }}
              onClick={() => openURLIframe("https://app.emergent.sh")}
            >
              <div
                className="flex items-center gap-2 text-transparent bg-clip-text"
                style={{
                  background:
                    "linear-gradient(270deg, #FCB949 40.48%, #E28C37 92.65%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                <p className="font-medium">Open Emergent </p>
              </div>
            </Button>
            <Button
              className=" h-[44px] w-[153px] px-4 py-3 flex space-x-2 justify-center items-center gap-4 text-white text-base font-medium rounded-[10px] backdrop-blur-[25px]"
              style={{
                background:
                  "radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%)",
              }}
              onClick={() => openURLIframe("https://help.emergent.sh")}
            >
              <div className="flex items-center gap-2">
                <p className="font-medium">Need Help? </p>
                <Mail color="white" strokeWidth={3} size={24} />
              </div>
            </Button>
          </div>
        </div>

        {currentView === "game" && !isMobile && (
          <>
            <div className="hidden md:flex flex-col flex-1 min-w-[50vw] w-full h-[calc(100vh-22px)] border  rounded-[12px] backdrop-blur-lg  border-1 border-white/10 justify-between  pb-8 pt-12 relative z-[2]">
              <p className="p-4 text-[20px] leading-[32px] font-normal text-center text-white opacity-[50%] font-nothing">
                • Play Games from Our Creators •
              </p>
              <div className="w-full h-full items-center flex flex-col justify-center  bg-[#090D0F]/10 backdrop-blur-[10px]">
                <GameBoyCarousel
                  setLoadUrl={setLoadUrl}
                  currentView={currentView}
                  setCurrentView={setCurrentView}
                />
              </div>
            </div>
          </>
        )}

        {currentView === "play" && !isMobile && (
          <div className="w-full backdrop-blur-lg flex-1 h-[calc(100vh-22px)] border  rounded-[12px]  border-1 border-white/10 justify-between relative z-[2]">
            <div className="p-6">
              <button
                onClick={() => setCurrentView("game")}
                className="flex items-center gap-2 p-1"
              >
                <img src={PixelArrow} alt="Right Arrow" className="w-6 h-6" />
                <span className="text-[20px] leading-[32px] text-[#FFFFFF99] font-nothing">
                  Go Back
                </span>
              </button>
            </div>
            <iframe
              src={loadUrl}
              title="Preview"
              className="min-w-[50vw] w-full h-full border-0"
            />
          </div>
        )}

        <SheetContent
          side="bottom"
          className="h-[calc(100vh-22px)] p-0 border-t-[#242424] rounded-t-[20px] gap-0"
        >
          <div
            className={cn(
              "flex items-center justify-between p-4",
              currentView === "play"
            )}
          >
            {currentView === "game" && (
              <p className="text-white text-[18px] opacity-[50%] font-nothing">
                Play Games from Our Creators
              </p>
            )}
            {currentView === "play" && (
              <button
                onClick={() => setCurrentView("game")}
                className="flex items-center gap-2 p-1"
              >
                <img src={PixelArrow} alt="Right Arrow" className="w-6 h-6" />
                <span className="text-[20px] leading-[32px] text-[#FFFFFF99] font-nothing">
                  Go Back
                </span>
              </button>
            )}

            <SheetClose>
              <div className=" w-10 h-10 flex items-center justify-center text-[#C4C4CC] hover:text-[#fff] hover:bg-white hover:bg-[#FFFFFF1A] bg-[#FFFFFF1A] rounded-lg transition-all duration-200 ease-in-out focus-visible:outline-none">
                <X className="w-6 h-6" />
              </div>
            </SheetClose>
          </div>

          {currentView === "game" && isMobile && (
            <div className="pb-4">
              <GameBoyCarousel
                setLoadUrl={setLoadUrl}
                currentView={currentView}
                setCurrentView={setCurrentView}
              />
            </div>
          )}

          {currentView === "play" && isMobile && (
            <iframe
              src={loadUrl}
              title="Preview"
              className="w-full h-full border-0"
            />
          )}

          {currentView === "play" && isMobile && (
            <div className="flex flex-col items-center gap-5 px-4 py-6">
              <div className="flex items-center gap-2">
                {" "}
                <img
                  src={loadingState === "completed" ? GreenCheck : ScreenImage}
                  alt="Screen"
                  className="w-6 h-6"
                />
                <span className="text-white text-[18px] text-white-glow">
                  {loadingState === "completed"
                    ? "Your Preview is Ready!"
                    : "Booting up the preview..."}
                </span>
              </div>
              {loadingState === "loading" && (
                <BoxLoader
                  progress={progress / 100}
                  boxClassname="bg-[#142C33] w-6 h-6 flex items-center justify-center p-[3px]"
                />
              )}
              {loadingState === "completed" && (
                <button
                  onClick={() => window.location.reload()}
                  className="w-full p-4 green-radial rounded-[10px] flex justify-between"
                  title="Launch"
                >
                  <span className="text-[#5FE55C] font-medium">Launch App</span>
                  <img src={GreenArrow} alt="play" className="w-6 h-6" />
                </button>
              )}
            </div>
          )}
        </SheetContent>
      </div>
    </Sheet>
  );
}
