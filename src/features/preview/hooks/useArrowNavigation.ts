import { useEffect } from 'react';

interface UseArrowNavigationProps {
  onNext: () => void;
  onPrevious: () => void;
  enabled?: boolean;
}

export const useArrowNavigation = ({ 
  onNext, 
  onPrevious, 
  enabled = true 
}: UseArrowNavigationProps) => {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowRight':
          event.preventDefault();
          onNext();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          onPrevious();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onNext, onPrevious, enabled]);
};
