import { createContext, useContext, useEffect, useState, useRef } from "react";
import { agentApi, CreditsBalance } from "../services/agentApi";
import { useAuth } from "./AuthContext";
import { captureError, trackEvent } from "../services/postHogService";

interface CreditsContextType {
  credits: number;
  loading: boolean;
  error: string | null;
  tier: "free" | "pro" | "starter" | "standard";
  refreshCredits: () => Promise<void>;
  creditResponse: CreditsBalance | null;
  upgradeDetails: {
    id: string,
    amount: number,
    name:string
  } | null;
  currentSubscriptionDetail: {
    id: string,
    amount: number,
    name:string
  } | null;
  getUpgradeTierName: () => string;
}

const CreditsContext = createContext<CreditsContextType>({
  credits: 0,
  loading: true,
  error: null,
  tier: "free",
  refreshCredits: async () => {},
  creditResponse: null,
  upgradeDetails: null,
  currentSubscriptionDetail: null,
  getUpgradeTierName: () => "Starter"
});

export function CreditsProvider({ children }: { children: React.ReactNode }) {
  const [credits, setCredits] = useState<number>(0);
  const [creditResponse , setCreditResponse] = useState<CreditsBalance | null>(null);
  const [upgradeDetails, setUpgradeDetails] = useState<{
    id: string,
    amount:number,
    name:string
  } | null>({
    id: "5d98824c-26c1-4dc4-822a-007ad1d5e684",
    amount: 10,
    name: "Emergent Starter"
  });
  const [tier, setTier] = useState<"free"| "pro" |"starter" | "standard">("free");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [currentSubscriptionDetail, setCurrentSubscriptionDetail] = useState<{
    id: string,
    amount:number,
    name:string,
  } | null>(null);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { user } = useAuth();

  const getTierInformation = (response?: CreditsBalance): "free" | "pro" | "starter" | "standard" => {
    const data = response || creditResponse;

    if (!data) {
      return "free";
    }

    if (!data.subscription || !data.subscription.id) {
      return "free";
    }

    const name = data.subscription.name.replaceAll(" ", "").toLowerCase();

    // Check for specific tier names in order of specificity (most specific first)
    if (name.includes("standard") || name.includes("standard")) {
      return "standard";
    } else if (name.includes("starter")) {
      return "starter";
    } else if (name.includes("pro")) {
      return "pro";
    } else {
      return "free";
    }
  }

  const getUpgradeTierName = (): string => {
    if (!upgradeDetails) return "Starter";

    const name = upgradeDetails.name.replaceAll(" ", "").toLowerCase();

    if (name.includes("standard")) {
      return "Standard";
    } else if (name.includes("starter")) {
      return "Starter";
    } else if (name.includes("pro")) {
      return "Pro";
    } else {
      return "Starter";
    }
  };

  const fetchCredits = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await agentApi.getCreditsBalance();
      // //console.log("Credits response:", response);
      if (response.error) {
        setError(response.error);
        setCredits(0);

        // Track credits API error response
        captureError(`Credits API returned error: ${response.error}`, {
          userId: user.id,
          errorType: 'api_response_error',
          apiResponse: response,
          context: 'credits_fetch'
        });

        trackEvent('credits_fetch_failed', {
          userId: user.id,
          errorType: 'api_response_error',
          errorMessage: response.error,
          hasInviteCode: !!user.user_metadata?.invite_code
        });
      } else {
        setCredits(response.ecu_balance || 0);
        setCreditResponse(response);

        // Always determine tier using the current response
        const tierName = getTierInformation(response);
        console.log("Setting tier to:", tierName, "from subscription:", response.subscription?.name);
        setTier(tierName);

        if(response.subscription && response.subscription.id){
          setCurrentSubscriptionDetail({
            id: response.subscription.id,
            name: response.subscription.name,
            amount: response.subscription.subscription_details?.amount || 0
          })
        } else {
          setCurrentSubscriptionDetail(null);
        }

        // Set upgrade details if subscription details exist
        if(response.subscription && response.subscription.subscription_details) {
          setUpgradeDetails({
            id: response.subscription.subscription_details.id,
            name: response.subscription.name,
            amount: response.subscription.subscription_details.amount
          })
        }
        // Keep the default fallback values if no subscription details from backend

        setError(null);
      }
    } catch (err : any) {
      console.error("Failed to fetch credits:", err);
      setError("Failed to fetch credits balance");
      setCredits(0);

      trackEvent('credits_fetch_failed', {
        userId: user.id,
        errorType: 'network_error',
        errorMessage: err ,
        hasInviteCode: !!user.user_metadata?.invite_code
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch credits when user changes
  useEffect(() => {
    if (user?.user_metadata.invite_code) {
      fetchCredits();
    }
  }, [user]);

  // Ensure tier is always in sync with creditResponse
  useEffect(() => {
    const newTier = getTierInformation();
    if (newTier !== tier) {
      console.log("Tier sync: updating from", tier, "to", newTier);
      setTier(newTier);
    }
  }, [creditResponse, tier]);

  // Debug tier changes
  useEffect(() => {
    console.log("Tier changed to:", tier, "Credit response:", creditResponse?.subscription);
  }, [tier, creditResponse]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  const refreshCredits = async () => {
    // If a refresh is already in progress, ignore this call
    if (isRefreshing) {
      console.log("Credits refresh already in progress, ignoring this call");
      return;
    }

    if (!user?.user_metadata.invite_code) {
      console.log("User is not verified, not refreshing credits");
      return;
    }

    // Set the refreshing state to true
    setIsRefreshing(true);

    try {
      // Perform the actual refresh
      await fetchCredits();
    } finally {
      // Clear any existing timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      // Set a timeout to clear the refreshing state after 5 seconds
      refreshTimeoutRef.current = setTimeout(() => {
        setIsRefreshing(false);
        refreshTimeoutRef.current = null;
      }, 5000); // 5 seconds cooldown
    }
  };

  const value = {
    credits,
    loading,
    tier,
    error,
    creditResponse,
    refreshCredits,
    upgradeDetails,
    currentSubscriptionDetail,
    getUpgradeTierName
  };

  return <CreditsContext.Provider value={value}>{children}</CreditsContext.Provider>;
}

export const useCredits = () => {
  const context = useContext(CreditsContext);
  if (!context) {
    throw new Error("useCredits must be used within a CreditsProvider");
  }
  return context;
};
