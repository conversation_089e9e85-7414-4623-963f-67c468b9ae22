import React, { createContext, useContext, useState, useCallback, useRef, useEffect, ReactNode } from 'react';
import { AlertLevel } from '@/components/ui/alert-banner';

export interface BannerConfig {
  show: boolean;
  level: AlertLevel;
  content: string;
  iconUrl?: string;
  dismissible?: boolean;
}

interface AlertContextType {
  bannerConfig: BannerConfig;
  bannerHeight: number;
  showBanner: (config: Omit<BannerConfig, 'show'>) => void;
  hideBanner: () => void;
  updateBanner: (updates: Partial<BannerConfig>) => void;
  setBannerHeight: (height: number) => void;
  showInfo: (content: string,  dismissible?: boolean) => void;
  showWarning: (content: string,  dismissible?: boolean, iconUrl?: string) => void;
  showError: (content: string,  dismissible?: boolean) => void;
  showSuccess: (content: string, dismissible?: boolean) => void;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

interface AlertProviderProps {
  children: ReactNode;
  initialConfig?: Partial<BannerConfig>;
}

export const AlertProvider: React.FC<AlertProviderProps> = ({ 
  children, 
  initialConfig 
}) => {
  const [bannerConfig, setBannerConfig] = useState<BannerConfig>({
    show: false,
    level: 'info',
    content: '',
    dismissible: true,
    ...initialConfig,
  });

  const [bannerHeight, setBannerHeight] = useState<number>(0);

  const showBanner = useCallback((config: Omit<BannerConfig, 'show'>) => {
    setBannerConfig({
      ...config,
      show: true,
    });

   
  }, []);

  const hideBanner = useCallback(() => {
    setBannerConfig(prev => ({ ...prev, show: false }));

  }, []);

  const updateBanner = useCallback((updates: Partial<BannerConfig>) => {
    setBannerConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const setBannerHeightCallback = useCallback((height: number) => {
    setBannerHeight(height);
  }, []);


  // Reset height when banner is hidden
  useEffect(() => {
    if (!bannerConfig.show) {
      setBannerHeight(0);
    }
  }, [bannerConfig.show]);

  // Convenience methods for different alert levels
  const showInfo = useCallback((content: string, dismissible = true) => {
    showBanner({ level: 'info', content, dismissible });
  }, [showBanner]);

  const showWarning = useCallback((content: string,  dismissible = true) => {
    showBanner({ level: 'warning', content, dismissible });
  }, [showBanner]);

  const showError = useCallback((content: string, dismissible = true) => {
    showBanner({ level: 'error', content, dismissible });
  }, [showBanner]);

  const showSuccess = useCallback((content: string, dismissible = true) => {
    showBanner({ level: 'success', content, dismissible });
  }, [showBanner]);

  const value: AlertContextType = {
    bannerConfig,
    bannerHeight,
    showBanner,
    hideBanner,
    updateBanner,
    setBannerHeight: setBannerHeightCallback,
    showInfo,
    showWarning,
    showError,
    showSuccess,
  };

  return (
    <AlertContext.Provider value={value}>
      {children}
    </AlertContext.Provider>
  );
};

export const useAlert = (): AlertContextType => {
  const context = useContext(AlertContext);
  if (context === undefined) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};
