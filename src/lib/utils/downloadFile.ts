/**
 * Utility functions for downloading files properly without opening in new tabs
 */

/**
 * Downloads a file from a URL by fetching it as a blob and triggering download
 * This ensures the file is saved to the user's system instead of opening in a new tab
 * @param url The URL of the file to download
 * @param filename The desired filename for the downloaded file
 */
export const downloadFileFromUrl = async (url: string, filename?: string): Promise<void> => {
  try {
    // Fetch the file as a blob
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch file: ${response.statusText}`);
    }
    
    const blob = await response.blob();
    
    // Create a blob URL and download link
    const blobUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = filename || 'download';
    link.style.display = 'none';
    
    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the blob URL
    URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error('Error downloading file:', error);
    // Fallback: try direct download with anchor element (without target="_blank")
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || 'download';
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

/**
 * Downloads a file from a base64 data URL
 * @param dataUrl The base64 data URL (e.g., "data:image/png;base64,...")
 * @param filename The desired filename for the downloaded file
 */
export const downloadFileFromDataUrl = (dataUrl: string, filename: string): void => {
  const link = document.createElement('a');
  link.href = dataUrl;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Downloads a file from a blob
 * @param blob The blob to download
 * @param filename The desired filename for the downloaded file
 */
export const downloadFileFromBlob = (blob: Blob, filename: string): void => {
  const blobUrl = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = blobUrl;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the blob URL
  URL.revokeObjectURL(blobUrl);
};
