export interface GitHubErrorResult {
  type: 'secret_protection' | 'remote_rejection' | 'generic';
  errorMessage: string;
  fullErrorText: string;
  learnMoreUrl?: string;
  shouldShowConflictUI?: boolean;
  shouldGenerateProposedBranch?: boolean;
}

export interface GitHubErrorHandlerOptions {
  defaultErrorMessage: string;
  needsForcePush?: boolean;
  allowConflictDetection?: boolean;
}

/**
 * Extracts error message from various possible error structures
 */
function extractErrorMessage(error: any, defaultMessage: string): { errorMessage: string; fullErrorText: string; errorString: string } {
  let errorMessage = defaultMessage;
  let fullErrorText = "";
  
  // Convert the entire error object to string to search through all nested content
  const errorString = JSON.stringify(error);

  if (error?.data?.detail) {
    errorMessage = error.data.detail;
    fullErrorText = error.data.detail;
  } else if (error?.data?.message) {
    errorMessage = error.data.message;
    fullErrorText = error.data.message;
  } else if (error?.message) {
    errorMessage = error.message;
    fullErrorText = error.message;
  }

  return { errorMessage, fullErrorText, errorString };
}

/**
 * Checks if the error is a GitHub secret push protection error
 */
function isSecretProtectionError(fullErrorText: string, errorString: string): boolean {
  const secretProtectionKeywords = [
    "GITHUB PUSH PROTECTION",
    "Push cannot contain secrets",
    "Secret push protection is enabled"
  ];

  return secretProtectionKeywords.some(keyword => 
    fullErrorText.includes(keyword) || errorString.includes(keyword)
  );
}

/**
 * Extracts learn more URL from error message
 */
function extractLearnMoreUrl(fullErrorText: string): string | undefined {
  const learnMoreUrlMatch = fullErrorText.match(/https:\/\/[^\s]+/g);
  return learnMoreUrlMatch?.find(url => 
    url.includes("atlas-kb.com") || url.includes("notion.site")
  );
}

/**
 * Checks if the error is a remote rejection error that requires force push
 */
function isRemoteRejectionError(fullErrorText: string, errorString: string): boolean {
  const rejectionKeywords = [
    "Updates were rejected because the remote contains work that you do not have locally",
    "remote contains work that you do not have locally"
  ];

  return rejectionKeywords.some(keyword => 
    fullErrorText.includes(keyword) || errorString.includes(keyword)
  );
}

/**
 * Generates a proposed branch name based on current date/time
 */
export function generateProposedBranchName(): string {
  const now = new Date();
  const day = now.getDate().toString().padStart(2, '0');
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const year = now.getFullYear().toString().slice(-2);
  const timeStr = now.toTimeString().slice(0, 2) + now.toTimeString().slice(3, 5);
  return `conflict_${day}${month}${year}_${timeStr}`;
}

/**
 * Main error handler for GitHub push operations
 * Processes errors and returns structured information for UI handling
 */
export function handleGitHubPushError(
  error: any, 
  options: GitHubErrorHandlerOptions
): GitHubErrorResult {
  const { defaultErrorMessage, needsForcePush = false, allowConflictDetection = true } = options;
  
  console.error("GitHub push error:", error);
  console.log("Full error object:", JSON.stringify(error, null, 2));

  const { errorMessage, fullErrorText, errorString } = extractErrorMessage(error, defaultErrorMessage);

  // Check for secret protection error first (highest priority)
  if (isSecretProtectionError(fullErrorText, errorString)) {
    console.log("GitHub secret push protection error detected");
    
    return {
      type: 'secret_protection',
      errorMessage,
      fullErrorText,
      learnMoreUrl: extractLearnMoreUrl(fullErrorText)
    };
  }

  // Check for remote rejection error (requires conflict handling)
  if (isRemoteRejectionError(fullErrorText, errorString)) {
    console.log("Remote rejection error detected");
    
    // Only show conflict UI if allowed and not already in force push mode
    const shouldShowConflictUI = allowConflictDetection && !needsForcePush;
    
    return {
      type: 'remote_rejection',
      errorMessage,
      fullErrorText,
      shouldShowConflictUI,
      shouldGenerateProposedBranch: shouldShowConflictUI
    };
  }

  // Generic error
  return {
    type: 'generic',
    errorMessage,
    fullErrorText
  };
}
