/**
 * GitHub utility functions for processing installations and repositories
 */

export interface GitHubInstallation {
  installation_id: string;
  account_login: string;
  account_type: string;
  user_github_login?: string;
  app_slug?: string;
  isPrimary?: boolean;
}

/**
 * Processes and sorts GitHub installations with primary account identification
 * @param installationsData - Raw installations data from API
 * @param primaryAccountName - The primary GitHub account name from user details
 * @returns Processed and sorted installations array
 */
export function processAndSortGitHubInstallations(
  installationsData: any[] | undefined,
  primaryAccountName: string | undefined
): GitHubInstallation[] {
  if (!installationsData || !primaryAccountName) {
    return installationsData || [];
  }

  // Update installations with isPrimary flag
  const processedInstallations = installationsData.map((installation: any) => ({
    ...installation,
    isPrimary: installation.account_login === primaryAccountName,
  }));

  // Sort installations: primary first, then organizations, then users
  return processedInstallations.sort((a: any, b: any) => {
    // Primary account always comes first
    if (a.isPrimary) return -1;
    if (b.isPrimary) return 1;

    // Then organizations
    if (a.account_type === 'Organization' && b.account_type !== 'Organization') return -1;
    if (a.account_type !== 'Organization' && b.account_type === 'Organization') return 1;

    // Then alphabetically by login
    return a.account_login.localeCompare(b.account_login);
  });
}
