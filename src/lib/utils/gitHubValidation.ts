import { isValidGitName, isValidRepoName } from "./gitValidation";

// Validation result types
export interface ValidationResult {
  isValid: boolean;
  errorMessage?: string;
  warningMessage?: string;
  successMessage?: string;
}

export interface RepositoryValidationContext {
  repositories: Array<{ name: string; [key: string]: any }>;
  isCreatingNewRepo: boolean;
  selectedInstallation: string;
}

export interface BranchValidationContext {
  branches: Array<{ name: string; [key: string]: any }>;
  selectedRepository: string;
  isCreatingNewRepo: boolean;
}

/**
 * Validates repository name and provides appropriate feedback messages
 */
export function validateRepositoryName(
  repoName: string,
  context: RepositoryValidationContext
): ValidationResult {
  const { repositories, isCreatingNewRepo, selectedInstallation } = context;

  // Early return if no repo name provided
  if (!repoName) {
    return {
      isValid: false,
      errorMessage: isCreatingNewRepo 
        ? "Enter a name for your new repository"
        : "Repository name is required"
    };
  }

  // Check if name follows Git naming rules
  if (!isValidRepoName(repoName)) {
    return {
      isValid: false,
      errorMessage: "Invalid repository name. Names must follow Git naming rules: only alphanumeric characters and - . / _ are allowed. Cannot contain spaces, consecutive dots (..), begin/end with /, end with ., or contain special characters."
    };
  }

  // Check if repository already exists (only for new repo creation)
  if (isCreatingNewRepo) {
    const repoExists = repositories.some(
      (repo) => repo.name.toLowerCase() === repoName.toLowerCase()
    );

    if (repoExists) {
      return {
        isValid: false,
        errorMessage: `Repository "${repoName}" already exists.`
      };
    }

    // Valid new repository
    return {
      isValid: true,
      successMessage: `New repo "${repoName}" will be created and your current changes will be pushed to it.`
    };
  }

  // Valid existing repository selection
  return { isValid: true };
}

/**
 * Validates branch name and provides appropriate feedback messages
 */
export function validateBranchName(
  branchName: string,
  context: BranchValidationContext
): ValidationResult {
  const { branches, selectedRepository, isCreatingNewRepo } = context;

  // Early return if no branch name provided
  if (!branchName) {
    return {
      isValid: false,
      errorMessage: "Branch name is required"
    };
  }

  // Check if name follows Git naming rules
  if (!isValidGitName(branchName)) {
    return {
      isValid: false,
      errorMessage: "Invalid branch name. Names must follow Git naming rules: only alphanumeric characters and - . / _ are allowed. Cannot contain spaces, consecutive dots (..), begin/end with /, end with ., or contain special characters."
    };
  }

  // For new repositories, any valid branch name is acceptable
  if (isCreatingNewRepo) {
    return { isValid: true };
  }

  // For existing repositories, check if branch exists
  if (selectedRepository) {
    const branchExists = branches.some((branch) => branch.name === branchName);
    
    if (!branchExists) {
      return {
        isValid: true,
        successMessage: `New branch "${branchName}" will be created and your current changes will be pushed to it.`
      };
    }
  }

  // Valid existing branch
  return { isValid: true };
}

/**
 * Validates proposed branch name for conflict resolution
 */
export function validateProposedBranchName(branchName: string): ValidationResult {
  if (!branchName) {
    return {
      isValid: false,
      errorMessage: "Branch name is required"
    };
  }

  if (!isValidGitName(branchName)) {
    return {
      isValid: false,
      errorMessage: "Invalid branch name. Names must follow Git naming rules."
    };
  }

  return { isValid: true };
}

/**
 * Validates form submission readiness
 */
export function validateFormSubmission(params: {
  jobId?: string;
  selectedInstallation: string;
  isCreatingNewRepo: boolean;
  newRepoName: string;
  selectedRepository: string;
  branchName: string;
  repositories: Array<{ name: string; [key: string]: any }>;
  isLoading: boolean;
  secretProtectionError?: any;
}): ValidationResult {
  const {
    jobId,
    selectedInstallation,
    isCreatingNewRepo,
    newRepoName,
    selectedRepository,
    branchName,
    repositories,
    isLoading,
    secretProtectionError
  } = params;

  // Check basic requirements
  if (!jobId) {
    return { isValid: false, errorMessage: "Job ID is required" };
  }

  if (!selectedInstallation) {
    return { isValid: false, errorMessage: "Please select a GitHub account" };
  }

  // Validate repository selection/creation
  if (isCreatingNewRepo) {
    const repoValidation = validateRepositoryName(newRepoName, {
      repositories,
      isCreatingNewRepo: true,
      selectedInstallation
    });
    if (!repoValidation.isValid) {
      return repoValidation;
    }
  } else {
    if (!selectedRepository) {
      return { isValid: false, errorMessage: "Please select a repository" };
    }
  }

  // Validate branch name
  const branchValidation = validateBranchName(branchName, {
    branches: [], // Not needed for basic validation
    selectedRepository,
    isCreatingNewRepo
  });
  if (!branchValidation.isValid) {
    return branchValidation;
  }

  // Check for loading states
  if (isLoading) {
    return { isValid: false, errorMessage: "Please wait for loading to complete" };
  }

  // Check for secret protection errors
  if (secretProtectionError) {
    return { isValid: false, errorMessage: "Please resolve secret protection issues before pushing" };
  }

  return { isValid: true };
}

/**
 * Helper function to check if a repository name already exists
 */
export function checkRepositoryExists(
  repoName: string,
  repositories: Array<{ name: string; [key: string]: any }>
): boolean {
  if (!repoName) return false;
  return repositories.some(
    (repo) => repo.name.toLowerCase() === repoName.toLowerCase()
  );
}

/**
 * Helper function to check if a branch exists
 */
export function checkBranchExists(
  branchName: string,
  branches: Array<{ name: string; [key: string]: any }>
): boolean {
  return branches.some((branch) => branch.name === branchName);
}

/**
 * Get validation status for UI styling
 */
export function getValidationStatus(result: ValidationResult): 'error' | 'warning' | 'success' | 'neutral' {
  if (!result.isValid) return 'error';
  if (result.warningMessage) return 'warning';
  if (result.successMessage) return 'success';
  return 'neutral';
}
