import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Loader2, Search, ChevronRight, ChevronDown } from "lucide-react";
import { agentApi } from "@/services/agentApi";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import CopyButton from '../CopyButton';

interface DebugInfoModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  containerId?: string;
  message?: any;
}

interface CollapsibleJsonProps {
  data: any;
  searchTerm: string;
  path?: string;
  isRoot?: boolean;
  defaultExpanded?: boolean;
}

// Component for rendering collapsible JSON with search highlighting
const CollapsibleJson: React.FC<CollapsibleJsonProps> = ({ 
  data, 
  searchTerm, 
  path = '', 
  isRoot = false,
  defaultExpanded = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded || isRoot);
  
  // Determine if this node or any children match the search term
  const hasMatch = searchTerm ? JSON.stringify(data).toLowerCase().includes(searchTerm.toLowerCase()) : false;
  
  // Highlight text that matches search term
  const highlightText = (text: string) => {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, i) => 
      regex.test(part) ? <span key={i} className="text-white bg-yellow-500/30">{part}</span> : part
    );
  };
  
  // If data is null or undefined
  if (data === null) return <span className="text-gray-500">null</span>;
  if (data === undefined) return <span className="text-gray-500">undefined</span>;
  
  // If data is a primitive type
  if (typeof data !== 'object') {
    const stringValue = String(data);
    return (
      <span className={typeof data === 'string' ? 'text-green-400' : 'text-blue-400'}>
        {typeof data === 'string' ? '"' : ''}
        {highlightText(stringValue)}
        {typeof data === 'string' ? '"' : ''}
      </span>
    );
  }
  
  // If data is an empty object or array
  if (Array.isArray(data) && data.length === 0) {
    return <span className="text-gray-400">[]</span>;
  }
  if (Object.keys(data).length === 0 && !Array.isArray(data)) {
    return <span className="text-gray-400">{"{}"}</span>;
  }
  
  // For arrays and objects
  const isArray = Array.isArray(data);
  const items = isArray ? data : Object.entries(data);
  
  return (
    <div className={`${!isRoot ? 'ml-4' : ''} ${hasMatch && searchTerm ? 'border-l-2 border-yellow-500 pl-2' : ''}`}>
      <div 
        className="flex items-center px-1 rounded cursor-pointer hover:bg-gray-800"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? 
          <ChevronDown className="w-4 h-4 mr-1 text-gray-400" /> : 
          <ChevronRight className="w-4 h-4 mr-1 text-gray-400" />
        }
        <span className="text-gray-300">
          {isArray ? 
            `Array(${items.length})` : 
            `Object{${Object.keys(data).length}}`
          }
        </span>
      </div>
      
      {isExpanded && (
        <div className="ml-2">
          {isArray ? (
            // Render array items
            items.map((item, index) => (
              <div key={`${path}.${index}`} className="flex">
                <span className="mr-2 text-gray-500">[{index}]:</span>
                <CollapsibleJson 
                  data={item} 
                  searchTerm={searchTerm} 
                  path={`${path}[${index}]`}
                  defaultExpanded={hasMatch && searchTerm !== ''}
                />
              </div>
            ))
          ) : (
            // Render object properties
            Object.entries(data).map(([key, value], index) => (
              <div key={`${path}.${key}`} className="flex">
                <span className="mr-2 text-purple-400">"{highlightText(key)}":</span>
                <CollapsibleJson 
                  data={value} 
                  searchTerm={searchTerm} 
                  path={`${path}.${key}`}
                  defaultExpanded={hasMatch && searchTerm !== ''}
                />
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export function DebugInfoModal({ isOpen, onOpenChange, containerId, message}: DebugInfoModalProps) {
  const { isMobile } = useScreenSize();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugData, setDebugData] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('formatted');
  const [stepNumber, setStepNumber] = useState<string>(message.step_number ? message.step_number.toString() : '');


  useEffect(() => {
    if (isOpen && containerId) {
      fetchDebugInfo();
    }
  }, [isOpen, containerId]);

  const fetchDebugInfo = async () => {
    if (!containerId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await agentApi.getDebugInfo(containerId, stepNumber ? parseInt(stepNumber) : undefined);
      setDebugData(data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch debug information');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  const renderJsonTree = (data: any) => {
    if (!data) return null;
    
    // Convert to string for highlighting if in raw mode
    if (activeTab === 'raw') {
      const jsonString = JSON.stringify(data, null, 2);
      
      if (!searchTerm) {
        return (
          <pre className="bg-[#1E1E1E] p-4 rounded-md overflow-auto h-full w-full text-sm">
            {jsonString}
          </pre>
        );
      }
      
      // Highlight search term in raw JSON
      const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
      const parts = jsonString.split(regex);
      
      return (
        <pre className="bg-[#1E1E1E] p-4 rounded-md overflow-auto h-full w-full text-sm">
          {parts.map((part, i) => 
            regex.test(part) ? 
              <span key={i} className="text-white bg-yellow-500/30">{part}</span> : 
              <span key={i}>{part}</span>
          )}
        </pre>
      );
    }
    
    // Use collapsible component for formatted view
    return (
      <div className="bg-[#1E1E1E] p-4 rounded-md overflow-auto h-full w-full text-sm font-mono">
        <CollapsibleJson data={data} searchTerm={searchTerm} isRoot={true} defaultExpanded={true} />
      </div>
    );
  };

  // Shared content component
  const renderContent = () => (
    <div className="flex-1 overflow-auto">
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2">Loading debug information...</span>
        </div>
      ) : error ? (
        <div className="p-4 text-red-500 rounded-md bg-red-500/10">
          {error}
        </div>
      ) : debugData ? (
        <>
          <div className="flex flex-col gap-4 px-4 mb-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Label htmlFor="step-number" className="block mb-1 text-sm font-medium">Step Number (optional)</Label>
                <div className="flex gap-2">
                  <Input
                    id="step-number"
                    type="number"
                    placeholder="Enter step number"
                    className="w-full"
                    value={stepNumber}
                    onChange={(e) => setStepNumber(e.target.value)}
                  />
                  <Button
                    variant="default"
                    onClick={fetchDebugInfo}
                    disabled={loading}
                  >
                    Fetch
                  </Button>
                </div>
              </div>
            </div>
            <div className="relative flex items-center">
              <Search className="absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 left-3 top-1/2" />
              <input
                type="text"
                placeholder="Search in JSON..."
                className="w-full pl-10 pr-4 py-2 bg-[#1E1E1E] border border-gray-700 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <CopyButton
                showIcon={true}
                iconOnly={true}
                className="ml-4 border-none bg-none"
                buttonClassName={buttonVariants({ variant: "outline", size: "sm" , className: "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 bg-white/10 border-white/20 border hover:bg-white/20 hover:text-white text-white/40"})}
                value={JSON.stringify(debugData, null, 2)}
                tooltipText="Copy"
                copiedTooltipText="Copied"
                feedbackType="tooltip"
                iconProps={{ size: 16 }}
                onCopy={() => { }}
              />
            </div>
          </div>

          <Tabs defaultValue="raw" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="formatted">Formatted</TabsTrigger>
              <TabsTrigger value="raw">Raw JSON</TabsTrigger>
            </TabsList>
            <TabsContent value="formatted" className="p-4 overflow-auto">
              {renderJsonTree(debugData)}
            </TabsContent>
            <TabsContent value="raw" className="p-4 overflow-auto">
              {renderJsonTree(debugData)}
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <div className="flex items-center justify-center h-64 text-gray-400">
          No debug information available
        </div>
      )}
    </div>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <BottomSheet
        trigger={<div />} // Empty trigger since we control it via props
        title={`Debug Information for ${containerId}`}
        open={isOpen}
        onOpenChange={onOpenChange}
        maxWidth="max-w-full"
        showDefaultFooter={false}
        footer={
          <div className="flex justify-end w-full">
            <Button onClick={handleClose}>Close</Button>
          </div>
        }
      >
        <div className="h-[80vh] flex flex-col">
          {renderContent()}
        </div>
      </BottomSheet>
    );
  }

  // For desktop, use Dialog
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[90vw] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Debug Information for {containerId}</DialogTitle>
        </DialogHeader>
        {renderContent()}
        <DialogFooter>
          <Button onClick={handleClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
