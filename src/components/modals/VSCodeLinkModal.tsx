import React from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import CopyButton from "../CopyButton";

interface VSCodeLinkModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  vscodeUrl: string;
  vscodePassword: string;
  onOpenInBrowser: () => void;
}

export const VSCodeLinkModal: React.FC<VSCodeLinkModalProps> = ({
  isOpen,
  onOpenChange,
  vscodeUrl,
  vscodePassword,
  onOpenInBrowser,
}) => {
  const { isMobile } = useScreenSize();

  // Shared content component
  const renderContent = ({showHeader = false}: { showHeader?: boolean } = {}) => (
    <div className="relative space-y-4">
      {showHeader && <div className="flex flex-col space-y-2">
        <DialogTitle className="text-base">VS Code Link</DialogTitle>
          <DialogDescription className="text-sm">
            Access VS Code in your browser with the following link and password
        </DialogDescription>
      </div>}
      <div className="w-full space-y-2">
        <p className="text-sm font-medium text-[#737780]">Link</p>
        <div className="flex items-center w-full gap-2">
          <input
            type="text"
            aria-label="Link"
            className="flex-1 w-full px-3 py-2 bg-[#1A1A1B] border border-[#242424] rounded-md text-[#DDDDE6] text-sm"
            value={vscodeUrl}
            readOnly
          />
          <CopyButton
              showIcon={true}
              iconOnly={true}
              className="border-none bg-none"
              buttonClassName={buttonVariants({ variant: "outline", size: "sm" , className: "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 bg-white/10 border-white/20 border hover:bg-white/20 hover:text-white text-white/40"})}
              value={vscodeUrl}
              tooltipText="Copy"
              copiedTooltipText="Copied"
              feedbackType="tooltip"
              iconProps={{ size: 16 }}
              onCopy={() => { }}
            />
        </div>
      </div>
      {vscodePassword && (
        <div className="w-full space-y-2">
          <p className="text-sm font-medium text-[#737780]">Password</p>
          <div className="flex items-center w-full gap-2">
            <input
              type="password"
              aria-label="Password"
              className="flex-1 w-full px-3 py-2 bg-[#1A1A1B] border border-[#242424] rounded-md text-[#DDDDE6] text-sm"
              value={vscodePassword}
              readOnly
            />
            <CopyButton
              showIcon={true}
              iconOnly={true}
              className="border-none bg-none"
              buttonClassName={buttonVariants({ variant: "outline", size: "sm" , className: "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 bg-white/10 border-white/20 border hover:bg-white/20 hover:text-white text-white/40"})}
              value={vscodePassword}
              tooltipText="Copy"
              copiedTooltipText="Copied"
              iconProps={{ size: 16 }}
              onCopy={() => { }}
            />
          </div>
        </div>
      )}
    </div>
  );

  // Shared footer component
  const renderFooter = () => (
    <div className="flex-row justify-between w-full sm:justify-between flex gap-4">
      <Button className="flex-1" variant="secondary" onClick={() => onOpenChange(false)}>
        <div className="flex w-full justify-center items-center">
        Cancel
        </div>
      </Button>
      <Button className="flex-1" onClick={onOpenInBrowser}>
      <div className="flex w-full justify-center items-center">Open in Browser
      </div>

      </Button>
    </div>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <BottomSheet
        trigger={<div />} // Empty trigger since we control it via props
        title=""
        description=""
        open={isOpen}
        onOpenChange={onOpenChange}
        maxWidth="max-w-full"
        showDefaultFooter={false}
        footer={renderFooter()}
      >
        <div className="h-[40dvh] flex flex-col px-3 pt-0">
          {renderContent({ showHeader: true })}
        </div>
      </BottomSheet>
    );
  }

  // For desktop, use Dialog
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="m-4 mx-auto max-w-[calc(100vw-32px)] sm:max-w-md">
        <DialogHeader>
          <DialogTitle>VS Code Link</DialogTitle>
          <DialogDescription>
            Access VS Code in your browser with the following link and password
          </DialogDescription>
        </DialogHeader>
        <div className="p-6">
          {renderContent()}
          </div>
          
        <DialogFooter className="flex-row justify-between w-full sm:justify-between">
          <DialogClose asChild>
            <Button variant="secondary">Cancel</Button>
          </DialogClose>
          <Button onClick={onOpenInBrowser}>Open in Browser</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};