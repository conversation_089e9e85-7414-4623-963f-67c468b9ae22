import { useState, useEffect } from "react";
import { Dialog, DialogContent } from "../ui/dialog";
import { Loader2, X } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { agentApi } from "@/services/agentApi";
import { captureError } from "@/services/postHogService";
import { useCredits } from "@/contexts";
import EmergentButton from "../EmergentButton";
import PaymentFailed from "@/assets/payment_failed.svg";
import StarsSVG from "@/assets/Stars.svg";
import SubscriptionSuccess from "@/assets/SubscriptionSucess.svg";

interface SubscriptionStatusModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  paymentId: string;
  initialStatus: "success" | "cancelled" | "failed";
}

export function SubscriptionStatusModal({
  isOpen,
  onOpenChange,
  paymentId,
  initialStatus,
}: SubscriptionStatusModalProps) {
  const [status, setStatus] = useState<
    "loading" | "success" | "pending" | "error"
  >(initialStatus === "success" ? "success" : "error");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const navigate = useNavigate();
  const { refreshCredits, tier, currentSubscriptionDetail, loading: creditsLoading } = useCredits();

  // Helper function to get tier display information from credits provider
  const getTierDisplayInfo = () => {
    // If credits are still loading, return null to prevent showing default values
    if (creditsLoading) {
      return null;
    }

    if (!tier || tier === "free") {
      return {
        name: "Free Tier",
        credits: 5
      };
    }

    switch(tier) {
      case "starter":
        return {
          name: "Starter Tier",
          credits: currentSubscriptionDetail?.amount ? currentSubscriptionDetail.amount * 5 : 50
        };
      case "standard":
        return {
          name: "Standard Tier",
          credits: 100
        };
      case "pro":
        return {
          name: "Pro Tier",
          credits: 100
        };
      default:
        return {
          name: "Starter Tier",
          credits: 50
        };
    }
  };

  useEffect(() => {
    const verifySubscription = async () => {
      if (initialStatus !== "success") {
        setStatus("error");
        setErrorMessage(
          initialStatus === "cancelled"
            ? "Subscription was cancelled."
            : "Subscription failed to process."
        );
        return;
      }

      try {
        const response = await agentApi.checkPaymentStatus(paymentId);

        if (response.status === "success") {
          setStatus("success");

          // Add a small delay to ensure backend has processed the subscription
          setTimeout(async () => {
            try {
              await refreshCredits();
              console.log("Credits refreshed after successful payment");
            } catch (error) {
              console.error("Failed to refresh credits after subscription success:", error);
              // Don't fail the success state if credits refresh fails
            }
          }, 1000); // 1 second delay
        } else if (response.status === "pending") {
          setStatus("pending");
        } else {
          setStatus("error");
          setErrorMessage(response.error || "Failed to verify subscription status.");
        }
      } catch (error) {
        console.error("Error verifying subscription:", error);
        captureError("Subscription verification failed", {
          paymentId,
          initialStatus,
          error: error instanceof Error ? error.message : String(error),
        });

        setStatus("error");
        setErrorMessage(
          "Failed to verify subscription status. Please contact support."
        );
      }
    };

    if (isOpen && initialStatus === "success") {
      verifySubscription();
    }
  }, [isOpen, paymentId, initialStatus]);

  // Log tier changes for debugging
  useEffect(() => {
    if (tier && status === "success") {
      console.log("Tier updated from context:", tier);
    }
  }, [tier, status]);

  // Log credits loading state changes for debugging
  useEffect(() => {
    console.log("Credits loading state:", creditsLoading, "Tier:", tier, "Current subscription:", currentSubscriptionDetail);
  }, [creditsLoading, tier, currentSubscriptionDetail]);

  const handleClose = async () => {
    // If subscription was successful, refresh credits one more time before closing
    if (status === "success") {
      try {
        await refreshCredits();
      } catch (error) {
        console.error("Failed to refresh credits on close:", error);
        // Continue closing even if refresh fails
      }
    }
    onOpenChange(false);
    navigate("/");
  };


  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95%]  md:max-w-[600px] bg-[#1C1C1F] text-white border-[#2E2F34] p-6 md:p-12">
        <div className="flex justify-end">
          <button
            type="button"
            title="Close"
            onClick={() => onOpenChange(false)}
            className="text-gray-400 hover:text-white"
          >
            <X size={24} />
          </button>
        </div>

        <div className="flex flex-col items-center justify-center py-6">
          {status === "loading" ? (
            // Loading state
            <div className="flex flex-col items-center justify-center gap-4  md:gap-[48px] font-brockmann">
              <div className="relative">
                <img src={StarsSVG} alt="Stars" className="w-16 h-16" />
                <Loader2 className="w-12 h-12 md:w-16 md:h-16 absolute top-0 left-0 animate-spin text-[#F3CA5F] opacity-70" />
              </div>
              <div className="flex flex-col items-center gap-1">
                <p className="text-[18px] md:text-[32px] md:leading-[36px] font-medium text-white text-nowrap">
                  Processing Subscription
                </p>
                <span className="font-['Inter'] md:mt-2 font-medium text-normal text-[#737780] text-center text-nowrap">
                  Verifying your subscription, please wait...
                </span>
              </div>
            </div>
          ) : status === "success" ? (
            // Success state
            <div className="flex flex-col items-center justify-center gap-4  md:gap-[48px] font-brockmann">
              <img src={SubscriptionSuccess} alt="Stars" className="w-12 h-12 md:w-16 md:h-16" />
              <div className="flex flex-col items-center md:gap-1">
                {getTierDisplayInfo() ? (
                  <>
                    <span className="text-[18px] md:text-[32px] leading-[36px] text-center font-medium bg-gradient-to-b from-[#F3CA5F] to-[#987315] text-transparent bg-clip-text">
                      {getTierDisplayInfo()!.name} Activated
                    </span>
                    <span className="font-['Inter'] md:mt-2 font-medium text-normal text-[#737780] text-center md:text-nowrap">
                      You now have access to {getTierDisplayInfo()!.credits} credits per month.
                    </span>
                  </>
                ) : (
                  <>
                    <span className="text-[18px] md:text-[32px] leading-[36px] text-center font-medium bg-gradient-to-b from-[#F3CA5F] to-[#987315] text-transparent bg-clip-text">
                      Subscription Activated
                    </span>
                    <span className="font-['Inter'] md:mt-2 font-medium text-normal text-[#737780] text-center md:text-nowrap">
                      Loading subscription details...
                    </span>
                  </>
                )}
              </div>
              <div className="flex flex-col w-full gap-2">
                <EmergentButton
                  onClick={handleClose}
                  variant="light"
                  className="w-full h-[45px]  md:h-full max-w-[200px] text-nowrap  md:max-w-[250px] mx-auto"
                  type="button"
                >
                  Keep Vibing
                </EmergentButton>
              </div>
            </div>
          ) : status === "pending" ? (
            // Pending state
            <div className="flex flex-col items-center justify-center gap-4  md:gap-[48px] font-brockmann">
              <img src={StarsSVG} alt="Stars" className="w-12 h-12 opacity-50 md:w-16 md:h-16" />
              <div className="flex flex-col items-center gap-1">
                <p className="text-[32px] leading-[36px] text-center text-nowrap w-full font-medium text-white">
                  Subscription Pending
                </p>
                <span className="font-['Inter'] md:mt-2 font-medium text-normal text-[#737780] text-center text-nowrap">
                  Your subscription is being processed. This may take a few minutes.
                </span>
              </div>
              <EmergentButton
                onClick={handleClose}
                variant="light"
                 className="w-full h-[45px]  md:h-full max-w-[200px] text-nowrap  md:max-w-[250px] mx-auto"
                type="button"
              >
                Continue
              </EmergentButton>
            </div>
          ) : (
            // Error state
            <div className="flex flex-col items-center justify-center gap-4 md:gap-[48px] font-brockmann">
              <div className="relative">
                <img src={PaymentFailed} alt="Failed" className="w-12 h-12 md:w-16 md:h-16" />
              </div>
              <div className="flex flex-col items-center gap-1">
                <p className="text-[18px] md:text-[32px] text-center  text-nowrap w-full md:leading-[36px] font-medium text-white">
                  Subscription Failed
                </p>
                <span className="font-['Inter'] md:mt-2 font-medium text-normal text-[#737780] text-center">
                  {errorMessage ||
                    <>Your subscription could not be processed.<br/> Please try again.</>}
                </span>
              </div>
              <EmergentButton
                onClick={handleClose}
                variant="light"
                className="w-full h-[45px]  md:h-full max-w-[200px] text-nowrap  md:max-w-[250px] mx-auto"
                type="button"
              >
                Close
              </EmergentButton>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default SubscriptionStatusModal;
