import React from "react";
import { Dialog, DialogContent, DialogClose } from "../ui/dialog";
import { X } from "lucide-react";
import { motion } from "framer-motion";
import ArrowDark from "@/assets/feature/arrow_dark.svg";

import ForkImage from "@/assets/feature/fork_feature.png";

import DollarCyanSVG from "@/assets/fork/dollar_cyan.svg";
import Clock<PERSON>yanSVG from "@/assets/fork/pace_cyan.svg";
import LikeCyanSVG from "@/assets/fork/like_cyan.svg";
import BranchGlow from "@/assets/feature/BranchGlow.svg";
import { markFeatureModalAsSeen } from "@/lib/utils/modalStateManager";

interface InfoInterface {
  title: string;
  description: string;
  icon: string;
}

export const INFO: InfoInterface[] = [
  {
    title: "Preserved session context",
    description: "All important context preserved in summary",
    icon: LikeCyanSVG,
  },

  {
    title: "Agent memory refreshed",
    description: "Fresh context window with clean slate",
    icon: ClockCyanSVG,
  },
  {
    title: "Less Spenditure",
    description: "Refreshed memory means lower token costs",
    icon: DollarCyanSVG,
  },
];

interface FeatureModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const FeatureModal: React.FC<FeatureModalProps> = ({
  isOpen,
  onOpenChange,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="md:w-[900px] max-h-[80vh] max-w-[90vw]  md:h-[650px] p-0 bg-[#18181A] rounded-2xl overflow-hidden">
        {/* Close Button */}
        <DialogClose className="absolute z-10 flex items-center justify-center w-8 h-8 text-white transition-all duration-200 rounded-full md:w-10 md:h-10 backdrop-blur-lg top-4 right-4 md:right-6 md:top-6 hover:bg-white/10 ">
          <X className="w-6 h-6 md:w-6 md:h-6" />
        </DialogClose>

        {/* Background Pattern */}
        {/* <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-br from-[#80FFF9]/10 via-transparent to-[#80FFF9]/5" />
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-[#80FFF9]/20 via-[#80FFF9]/5 to-transparent rounded-full blur-3xl" />
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-radial from-[#80FFF9]/15 via-[#80FFF9]/3 to-transparent rounded-full blur-3xl" />
        </div> */}

        {/* Content */}
        <div className="relative flex w-[inherit] h-full max-md:flex-col-reverse">
          {/* Left Side - Content */}
          <div className="flex flex-col justify-center flex-1 gap-4 md:px-[40px] md:py-12">
            <div className="flex flex-col gap-2">
              <div className="flex flex-col gap-4 max-md:px-5 max-md:pt-6">
                <img
                  src={BranchGlow}
                  alt="Branch"
                  className="w-10 h-10 scale-[1.5] max-md:hidden"
                />
                {/* Title */}
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-[20px] md:text-[28px] leading-[32px] font-semibold tracking-[-2%] text-white "
                >
                  Introducing Forks
                </motion.h1>
              </div>

              {/* Description */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-[#737780] max-md:px-5  text-[14px] font-['Inter']  md:text-[15px] text-wrap whitespace-pre-wrap break-words leading-[20px]  md:leading-[24px]"
              >
                Continue your work in a new conversation while keeping the
                important context, perfect for continuing complex projects
                without losing progress.
              </motion.p>
            </div>

            {/* Features List */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex md:grid md:grid-cols-1 gap-[10px] max-md:pl-5 overflow-x-auto hide-scrollbar"
            >
              {INFO.map((item, index) => (
                <div
                  key={index}
                  className="flex rounded-[8px] max-md:min-w-[70%] max-md:my-2 max-md:flex-col-reverse max-md:items-start p-3 md:px-5 md:py-4 items-center w-full text-center bg-[#80FFF90A] gap-4"
                >
                  <div className="flex flex-col items-start w-full gap-[2px] md:gap-[6px]">
                    <h3 className="text-[14px] md:text-[16px] text-[#80FFF9] font-medium">
                      {item.title}
                    </h3>
                    <p className="text-[12px] md:text-[14px] font-[500] text-start text-[#80FFF966] font-['Inter']">
                      {item.description}
                    </p>
                  </div>
                  <img src={item.icon} alt={item.title} className="w-5 h-5" />
                </div>
              ))}
            </motion.div>

            {/* Get Started Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
              className="max-md:px-5 max-md:pb-6"
            >
              <button
                type="button"
                onClick={() => {
                  markFeatureModalAsSeen();
                  onOpenChange(false);
                }}
                className="flex items-center max-md:max-h-[48px] mt-4 md:mt-[36px] justify-center w-full gap-4 text-[16px] font-semibold text-black transition-all duration-200 transform bg-white shadow-lg h-14 rounded-xl hover:bg-gray-100 hover:shadow-xl hover:-translate-y-1"
              >
                Get Started
                <img src={ArrowDark} alt="Arrow" className="w-5 h-5" />
              </button>
            </motion.div>
          </div>

          {/* Right Side - Preview/Illustration */}
          <div className="relative max-md:max-h-[200px] flex-1 overflow-hidden bg-blue-400">
            <img
              alt="Fork Image"
              src={ForkImage}
              className="object-cover w-full h-full md:object-fill  max-md:scale-[1.05]"
            />
            {/* Decorative Elements */}
            <div className="absolute top-1/4 right-8 w-32 h-32 bg-gradient-radial from-[#80FFF9]/20 to-transparent rounded-full blur-2xl" />
            <div className="absolute bottom-1/4 right-16 w-24 h-24 bg-gradient-radial from-[#80FFF9]/15 to-transparent rounded-full blur-xl" />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FeatureModal;
