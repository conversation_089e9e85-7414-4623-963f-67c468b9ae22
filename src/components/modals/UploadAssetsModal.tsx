import React, { useState, useCallback, useRef } from "react";
import { X, Upload, FileText, File, Image, Video, Archive, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useFileAttachments } from "@/hooks/useFileAttachments";
import { motion, AnimatePresence } from "framer-motion";
import UploadIcon from "@/assets/upload/upload.svg"
import { FileAttachmentData } from "@/types/artifact";
import { useToast } from "@/hooks/use-toast";
import * as TooltipPrimitive from "@radix-ui/react-tooltip";

import CommentIcon from "@/assets/upload/comment.svg"
import CommentedIcon from "@/assets/upload/commented.svg"

import DeleteIcon from "@/assets/upload/delete.svg"
import DeleteActiveIcon from "@/assets/upload/deleteActive.svg"
import { agentApi } from "@/services/agentApi";
import ShareUp from "@/assets/upload/arrow_circle_up.svg"

import LinesBG from "@/assets/upload/lines.svg"

import CSVIcon from "@/assets/files/svg.svg"
import PDFIcon from "@/assets/files/pdf.svg"
import OtherIcon from "@/assets/files/pdf.svg"

import UploadNormal from "@/assets/upload/upload_normal.svg"

interface UploadAssetsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  jobId?: string;
  onUploadComplete?: (artifactId: string, fileName: string) => void;
  onUploadError?: (fileName: string, error: string) => void;
  handleSendMessage?: (content: string, images: any[]) => void;
}

// Extended file data with description
interface ExtendedFileData extends FileAttachmentData {
  description?: string;
}

// Helper function to check if file type is previewable as image
const isPreviewableImage = (mimeType: string, fileName: string) => {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
  return imageTypes.includes(mimeType.toLowerCase()) ||
         /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(fileName);
};

// Helper function to check if file type is previewable as SVG
const isPreviewableSVG = (mimeType: string, fileName: string) => {
  return mimeType === 'image/svg+xml' || fileName.toLowerCase().endsWith('.svg');
};

// Helper function to get file icon based on mime type or extension
const getFileIcon = (mimeType: string, fileName: string) => {
  if (mimeType.startsWith('image/')) return Image;
  if (mimeType.startsWith('video/')) return Video;
  if (mimeType.startsWith('text/') || fileName.endsWith('.txt')) return FileText;
  if (mimeType.includes('zip') || mimeType.includes('tar') || mimeType.includes('rar')) return Archive;
  if (fileName.endsWith('.pdf')) return FileText;
  if (fileName.endsWith('.csv') || fileName.endsWith('.xlsx')) return FileText;
  return File;
};

// Helper function to get custom file icon for specific types
const getCustomFileIcon = (mimeType: string, fileName: string) => {
  const lowerFileName = fileName.toLowerCase();

  // PDF files
  if (lowerFileName.endsWith('.pdf') || mimeType === 'application/pdf') {
    return PDFIcon;
  }

  // Spreadsheet files
  if (lowerFileName.endsWith('.csv') ||
      lowerFileName.endsWith('.xlsx') ||
      lowerFileName.endsWith('.xls') ||
      mimeType.includes('spreadsheet') ||
      mimeType.includes('csv')) {
    return CSVIcon;
  }

  // SVG files (use CSV icon as placeholder for now)
  if (lowerFileName.endsWith('.svg') || mimeType === 'image/svg+xml') {
    return CSVIcon;
  }

  return OtherIcon; // Return null to use Lucide icons
};

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

export function UploadAssetsModal({
  isOpen,
  onOpenChange,
  jobId,
  onUploadComplete,
  onUploadError,
  handleSendMessage
}: UploadAssetsModalProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<ExtendedFileData[]>([]);
  const [showCommentInput, setShowCommentInput] = useState<Set<number>>(new Set());
  const [isUploading, setIsUploading] = useState(false);
  const [currentUploadingIndex, setCurrentUploadingIndex] = useState<number | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<Set<number>>(new Set());
  const fileInputRef = useRef<HTMLInputElement>(null);
  const objectUrlsRef = useRef<Set<string>>(new Set());
  const { toast } = useToast();

  // Helper function to create and track object URLs
  const createObjectURL = useCallback((file: File) => {
    const url = URL.createObjectURL(file);
    objectUrlsRef.current.add(url);
    return url;
  }, []);

  // Cleanup object URLs when component unmounts or files change
  React.useEffect(() => {
    return () => {
      // Cleanup all object URLs when component unmounts
      objectUrlsRef.current.forEach(url => {
        URL.revokeObjectURL(url);
      });
      objectUrlsRef.current.clear();
    };
  }, []);

  // Initialize file attachments hook
  const fileAttachments = useFileAttachments({
    maxFiles: 5,
    maxSizeInMB: 5,
    allowedTypes: [], // Allow any file type as per requirements
    jobId,
    onUploadComplete: (artifactId, fileName) => {
      console.log(`Upload completed: ${fileName} (${artifactId})`);
      onUploadComplete?.(artifactId, fileName);
    },
    onUploadError: (fileName, error) => {
      console.error(`Upload failed for ${fileName}:`, error);
      onUploadError?.(fileName, error);
    },
  });

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | null) => {
    // Prevent file selection during upload
    if (isUploading) return;

    if (files && files.length > 0) {
      const maxFiles = 5;
      const maxSizeInBytes = 5 * 1024 * 1024; // 5MB in bytes

      // Check if adding these files would exceed the limit
      const totalFilesAfterAdd = selectedFiles.length + files.length;
      if (totalFilesAfterAdd > maxFiles) {
        toast({
          title: "Too many files selected",
          description: `You can only select up to ${maxFiles} assets at a time. Currently selected: ${selectedFiles.length}`,
          variant: "destructive",
          duration: 4000,
        });
        return;
      }

      // Validate each file size
      const oversizedFiles: string[] = [];
      const validFiles: ExtendedFileData[] = [];

      Array.from(files).forEach(file => {
        if (file.size > maxSizeInBytes) {
          oversizedFiles.push(file.name);
        } else {
          validFiles.push({
            file,
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
            description: ''
          });
        }
      });

      // Show error for oversized files
      if (oversizedFiles.length > 0) {
        toast({
          title: "Files too large",
          description: `The following files exceed the 5MB limit and were not added: ${oversizedFiles.join(', ')}`,
          variant: "destructive",
          duration: 5000,
        });
      }

      // Add valid files
      if (validFiles.length > 0) {
        setSelectedFiles(prev => [...prev, ...validFiles]);
      }
    }
  }, [selectedFiles.length, isUploading]);

  // Handle browse files button click
  const handleBrowseClick = useCallback(() => {
    // Prevent browse during upload
    if (isUploading) return;
    fileInputRef.current?.click();
  }, [isUploading]);

  // Handle file input change
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  // Handle description change for a file
  const handleDescriptionChange = useCallback((index: number, description: string) => {
    // Prevent description changes during upload
    if (isUploading) return;
    setSelectedFiles(prev => prev.map((file, i) =>
      i === index ? { ...file, description } : file
    ));
  }, [isUploading]);

  // Handle file removal
  const handleRemoveFile = useCallback((index: number) => {
    // Prevent file removal during upload
    if (isUploading) return;

    setSelectedFiles(prev => {
      // Clean up object URL for the removed file if it was previewable
      const fileToRemove = prev[index];
      if (fileToRemove && (isPreviewableImage(fileToRemove.type, fileToRemove.name) || isPreviewableSVG(fileToRemove.type, fileToRemove.name))) {
        // Note: We can't easily track individual URLs, but they'll be cleaned up on unmount
        // This is acceptable for the current use case
      }
      return prev.filter((_, i) => i !== index);
    });

    // Also remove from comment input visibility set
    setShowCommentInput(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      // Adjust indices for remaining files
      const adjustedSet = new Set<number>();
      newSet.forEach(i => {
        if (i > index) {
          adjustedSet.add(i - 1);
        } else {
          adjustedSet.add(i);
        }
      });
      return adjustedSet;
    });
  }, [isUploading]);

  // Handle comment button toggle
  const handleCommentToggle = useCallback((index: number, e?: React.MouseEvent) => {
    // Prevent comment toggle during upload
    if (isUploading) return;

    // Prevent event propagation to avoid conflicts with onBlur
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    setShowCommentInput(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, [isUploading]);

  // Reset upload states
  const resetUploadStates = useCallback(() => {
    setIsUploading(false);
    setCurrentUploadingIndex(null);
    setUploadedFiles(new Set());
  }, []);

  // Reset states when modal opens/closes
  React.useEffect(() => {
    if (!isOpen) {
      resetUploadStates();
    }
  }, [isOpen, resetUploadStates]);

  const handleUploadAll = useCallback(async () => {
    if (!jobId || selectedFiles.length === 0 || isUploading) return;

    setIsUploading(true);

    try {

      await fileAttachments.uploadAllFiles(selectedFiles);

      setTimeout(() => {
        setSelectedFiles([]);
        onOpenChange(false);
      }, 1000);

    } catch (error) {
      console.error('Upload process failed:', error);
    } finally {
      setIsUploading(false);
      setCurrentUploadingIndex(null);
    }
  }, [selectedFiles, fileAttachments, jobId, onOpenChange, isUploading, handleSendMessage]);

  // Handle drag events
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Prevent drag during upload
    if (isUploading) return;
    setIsDragging(true);
  }, [isUploading]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Prevent drag during upload
    if (isUploading) return;
    // Only set to false if we're leaving the modal content area
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  }, [isUploading]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    // Prevent drop during upload
    if (isUploading) return;

    const files = e.dataTransfer.files;
    handleFileSelect(files);
  }, [handleFileSelect, isUploading]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[49] flex items-center justify-center bg-[#0e0e0f50] backdrop-blur-[5px]">
      <div
        className="max-w-[740px] relative w-[95vw] p-0 bg-[#18181A] border border-[#242424] rounded-[16px] overflow-hidden flex flex-col font-['Inter']"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className={cn("flex items-center justify-between p-4  md:p-8", selectedFiles.length > 0 ? "md:p-6 border-b border-[#242424] " : "")}>
          <div className="flex flex-col gap-1 md:gap-[10px]">
            <h2 className="text-[18px] md:text-[22px] font-medium text-white md:leading-[32px]">
              Upload Assets to share with the Agent
            </h2>
            <p className="text-[13px] md:text-[15px] text-[#737780] md:leading-[24px]">
              Upload assets and media files that your agent can access and integrate into your code.
            </p>
          </div>
          <button
            type="button"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
            className={cn(
              "p-2 absolute top-6 right-6 bg-[#FFFFFF05] backdrop-blur-lg hover:bg-[#242424] rounded-lg transition-colors",
              isUploading && "opacity-50 cursor-not-allowed"
            )}
            aria-label="Close modal"
          >
            <X className="w-5 h-5 text-[#737780]" />
          </button>
        </div>

        {/* Main Content */}
        <div className={cn("flex flex-col flex-1 p-4 md:p-8 pt-0 md:pt-0 overflow-hidden overflow-y-auto max-h-[400px]", selectedFiles.length > 0 ? "md:p-6 pt-0 md:pt-0" : "")}>
          {/* Assets Header */}
          {
            selectedFiles.length > 0 && <div className="md:pt-4 pt-3 flex items-center justify-between backdrop-blur-lg pb-2 z-[10] bg-[#18181A]">
            <div className="flex items-center gap-2">
              <h3 className="text-[16px] font-medium text-[#737780] font-['Inter']">
                {selectedFiles.length} Uploaded Assets
              </h3>
            </div>
            <div className="flex items-center gap-2">
              {/* Upload Assets Button */}
              <TooltipPrimitive.Provider>
                <TooltipPrimitive.Root>
                  <TooltipPrimitive.Trigger asChild>
                    <button
                      type="button"
                      onClick={handleBrowseClick}
                      disabled={selectedFiles.length >= 5 || isUploading}
                      className={cn(
                        "flex items-center gap-2 px-4 py-2 radial-bg rounded-[8px] text-[#80FFF9] hover:opacity-[0.80] font-medium text-[14px] font-['Inter'] transition-all duration-200 disabled:opacity-20",
                        isUploading && "cursor-not-allowed"
                      )}
                    >
                    <img src={UploadNormal} alt="Upload" className="w-6 h-6" />
                     Upload Assets
                    </button>
                  </TooltipPrimitive.Trigger>
                  {(selectedFiles.length >= 5 || isUploading) && (
                    <TooltipPrimitive.Portal>
                      <TooltipPrimitive.Content
                        className="bg-[#272829]/95 backdrop-blur-md border-[#2E2F34] text-white shadow-lg z-[9999] px-3 py-2 rounded-md text-sm"
                        side="bottom"
                        sideOffset={8}
                      >
                        {isUploading ? "Upload in progress..." : "You can only upload 5 items at a time"}
                      </TooltipPrimitive.Content>
                    </TooltipPrimitive.Portal>
                  )}
                </TooltipPrimitive.Root>
              </TooltipPrimitive.Provider>
            </div>
          </div>
          }

          {/* Assets List */}
          <div className="flex-1">
            {selectedFiles.length === 0 ? (
              /* Empty State with Upload Area */
              <div
                className={cn(
                  "relative border-2 overflow-clip border-dashed border-[#80FFF914] rounded-[12px] p-6 md:p-12 text-center flex items-center flex-col transition-all duration-200 h-full min-h-[300px] justify-center",
                  isUploading && "opacity-50 pointer-events-none",
                  isDragging && "border-[#80FFF9] bg-[#FFFFFF05]"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                {/* Upload Icon */}
                <img src={UploadIcon} alt="Upload" className={cn("w-12 h-12 mb-4", isDragging && "opacity-0")} />
                <img src={LinesBG} alt="Lines" className="absolute top-0 bottom-0 left-0  right-0 max-md:scale-[3] w-full h-full " />

                {/* Upload Text */}
                <div className={cn("mb-6 space-y-[6px] z-1", isDragging && "opacity-0")}>
                  <p className="text-[18px] font-medium text-[#80FFF9]">
                    Choose a file or drag & drop it here
                  </p>
                  <p className="text-[14px] text-[#3D6664] font-medium font-['Inter']">
                    Upload up to 5 files (5MB each) - Any file type supported
                  </p>
                </div>

                {/* Browse Files Button */}
                <TooltipPrimitive.Provider>
                  <TooltipPrimitive.Root>
                    <TooltipPrimitive.Trigger asChild>
                      <button
                        type="button"
                        onClick={handleBrowseClick}
                        disabled={isUploading}
                        className={cn(
                          "px-4 py-2 radial-bg rounded-[10px] text-[#80FFF9] hover:opacity-[0.80] font-medium text-[14px] font-['Inter'] transition-all duration-200",
                          isUploading && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        Browse Files
                      </button>
                    </TooltipPrimitive.Trigger>
                    {isUploading && (
                      <TooltipPrimitive.Portal>
                        <TooltipPrimitive.Content
                          className="bg-[#272829]/95 backdrop-blur-md border-[#2E2F34] text-white shadow-lg z-[9999] px-3 py-2 rounded-md text-sm"
                          side="bottom"
                          sideOffset={8}
                        >
                          Upload in progress...
                        </TooltipPrimitive.Content>
                      </TooltipPrimitive.Portal>
                    )}
                  </TooltipPrimitive.Root>
                </TooltipPrimitive.Provider>

                {/* Drag overlay */}
                <AnimatePresence>
                  {isDragging && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="absolute inset-0 bg-[#80FFF9]/10 rounded-[12px] flex items-center justify-center"
                    >
                      <div className="text-center">
                        <Upload className="w-12 h-12 text-[#80FFF9] mx-auto mb-2" />
                        <p className="text-[16px] font-medium text-[#80FFF9] font-['Inter']">
                          Drop files here
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              /* Files List */
              <div className="flex flex-col gap-3 md:gap-4 md:mt-4">
                {selectedFiles.map((file, index) => {
                  const FileIcon = getFileIcon(file.type, file.name);
                  const isCurrentlyUploading = currentUploadingIndex === index;
                  const isUploaded = uploadedFiles.has(index);

                  return (
                    <div
                      key={index}
                      className={cn(
                        "relative transition-all overflow-clip duration-300 rounded-[12px] h-full group border",
                        isCurrentlyUploading ? "border-[#80FFF9] opacity-100" :
                        isUploaded ? "border-[#FFFFFF0F] opacity-100" :
                        "border-[#FFFFFF0F] opacity-60 hover:opacity-100",
                        showCommentInput.has(index) ? "opacity-100" : ""
                      )}
                    >
                      <div className="flex items-center gap-4 p-3 bg-[#1D1D1E]">
                        {/* File Icon/Preview */}
                        <div className="w-10 h-10 flex items-center justify-center rounded-lg bg-[#FFFFFF0A] flex-shrink-0 overflow-hidden">
                          {isPreviewableImage(file.type, file.name) ? (
                            <img
                              src={createObjectURL(file.file)}
                              alt={file.name}
                              className="object-cover w-full h-full rounded-lg"
                              onError={(e) => {
                                // Fallback to icon if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const parent = target.parentElement;
                                if (parent) {
                                  const fallbackIcon = parent.querySelector('.fallback-icon');
                                  if (fallbackIcon) {
                                    (fallbackIcon as HTMLElement).style.display = 'block';
                                  }
                                }
                              }}
                            />
                          ) : isPreviewableSVG(file.type, file.name) ? (
                            <img
                              src={createObjectURL(file.file)}
                              alt={file.name}
                              className="w-6 h-6"
                              onError={(e) => {
                                // Fallback to icon if SVG fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const parent = target.parentElement;
                                if (parent) {
                                  const fallbackIcon = parent.querySelector('.fallback-icon');
                                  if (fallbackIcon) {
                                    (fallbackIcon as HTMLElement).style.display = 'block';
                                  }
                                }
                              }}
                            />
                          ) : getCustomFileIcon(file.type, file.name) ? (
                            <img
                              src={getCustomFileIcon(file.type, file.name)!}
                              alt="File"
                              className="w-5 h-5"
                            />
                          ) : (
                            <FileIcon className="w-5 h-5 text-[#FFFFFF60]" />
                          )}

                          {/* Fallback icon (hidden by default, shown when image fails to load) */}
                          {(isPreviewableImage(file.type, file.name) || isPreviewableSVG(file.type, file.name)) && (
                            <div className="fallback-icon" style={{ display: 'none' }}>
                              {getCustomFileIcon(file.type, file.name) ? (
                                <img
                                  src={getCustomFileIcon(file.type, file.name)!}
                                  alt="File"
                                  className="w-5 h-5"
                                />
                              ) : (
                                <FileIcon className="w-5 h-5 text-[#FFFFFF60]" />
                              )}
                            </div>
                          )}
                        </div>

                        {/* File Info */}
                        <div className="flex-1 min-w-0">
                          <h3 className="text-[#E6E6E6] font-medium text-[14px] font-['Inter'] truncate mb-1">
                            {file.name}
                          </h3>
                          <div className="text-[12px] text-white/50">
                            {formatFileSize(file.size)}
                          </div>
                        </div>

                        {/* Action Icons */}
                        <div className="flex items-center gap-2">
                          <button
                            type="button"
                            onClick={(e) => handleCommentToggle(index, e)}
                            disabled={isUploading || isCurrentlyUploading || isUploaded}
                            className={cn(
                              "p-2 hover:bg-[#FFFFFF0F] rounded-lg transition-colors group",
                              showCommentInput.has(index) ? "opacity-100 bg-[#FFFFFF0F]" : "opacity-60 hover:opacity-100",
                              (isUploading || isCurrentlyUploading || isUploaded) && "opacity-30 cursor-not-allowed"
                            )}
                            title={file.description ? "Edit comment" : "Add comment"}
                          >
                            <img
                              src={file.description ? CommentedIcon : CommentIcon}
                              alt={file.description ? "Commented" : "Comment"}
                              className="w-6 h-6 group-hover:hidden"
                            />
                            <img
                              src={!file.description ? CommentIcon : CommentedIcon}
                              alt="Comment Active"
                              className="hidden w-6 h-6 group-hover:block"
                            />
                          </button>
                          <button
                            type="button"
                            onClick={() => handleRemoveFile(index)}
                            disabled={isUploading || isCurrentlyUploading}
                            className={cn(
                              "p-2 rounded-lg transition-colors hover:bg-[#ED5B5B0F] group",
                              (isUploading || isCurrentlyUploading) && "opacity-30 cursor-not-allowed"
                            )}
                            title="Delete"
                          >
                            <img src={DeleteIcon} alt="Delete" className="w-6 h-6 group-hover:hidden" />
                            <img src={DeleteActiveIcon} alt="Delete Active" className="hidden w-6 h-6 group-hover:block" />
                          </button>
                        </div>
                      </div>

                      {/* Description Input - Only show when comment button is clicked */}
                      {showCommentInput.has(index) && (
                        <div className="bg-[#18181A]">
                          <input
                            type="text"
                            placeholder="Add comments for agent"
                            value={file.description || ''}
                            onChange={(e) => handleDescriptionChange(index, e.target.value)}
                            disabled={isUploading}
                            className={cn(
                              "w-full bg-transparent pl-4 py-4 font-medium text-[14px] text-[#FFFFFF60] placeholder-[#4A4A4D] font-['Inter'] focus:outline-none transition-colors",
                              isUploading && "opacity-50 cursor-not-allowed"
                            )}
                            autoFocus
                          />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        {selectedFiles.length > 0 && (
          <div className="flex items-center justify-end gap-4 p-6 border-t border-[#242424]/60">
            <button
              type="button"
              onClick={() => onOpenChange(false)}
              disabled={isUploading}
              className={cn(
                "px-6 py-3 bg-[#272729] hover:bg-[#444] rounded-full text-[#DCDCE5] font-medium text-[16px] transition-all duration-200",
                isUploading && "opacity-50 cursor-not-allowed"
              )}
            >
              Cancel
            </button>

            <button
              type="button"
              onClick={handleUploadAll}
              disabled={!jobId || isUploading}
              className={cn(
                "md:px-6 p-3 md:py-3 bg-white hover:bg-white/90 rounded-full text-[#0E0E0F] font-semibold text-[14px]  md:text-[16px] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",
                isUploading && "cursor-not-allowed"
              )}
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Sharing with Agent...
                </>
              ) : (
                <>
                  <img src={ShareUp} alt="Share" className="w-6 h-6" />
                  Share with Agent
                </>
              )}
            </button>
          </div>
        )}

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileInputChange}
          disabled={isUploading}
          className="hidden"
          aria-label="Upload files"
        />
      </div>
    </div>
  );
}
