import { cn } from "@/lib/utils";
import { useState } from "react";
import { ImagePreviewUrl } from "./ImagePreviewUrl";
import { File, FileText, Image, Video, Archive, Download } from "lucide-react";
import PDFIcon from "@/assets/files/pdf.svg";
import CSVIcon from "@/assets/files/svg.svg";
import OtherIcon from "@/assets/files/pdf.svg";
import { downloadFileFromUrl } from "@/lib/utils/downloadFile";

interface ArtifactData {
  name: string;
  url: string;
  artifact_type?: string;
  mime_type?: string;
  file_size?: number;
}

interface ArtifactGalleryProps {
  images: ArtifactData[];
  className?: string;
}

// Helper function to check if file type is previewable as image
const isPreviewableImage = (fileName: string, url: string, mimeType?: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
  const hasImageExtension = imageExtensions.some(ext =>
    fileName.toLowerCase().endsWith(ext) || url.toLowerCase().includes(ext)
  );

  // Check mime type if available
  if (mimeType && mimeType.startsWith('image/')) {
    return true;
  }

  // Also check if URL contains common image hosting patterns
  const imageHostingPatterns = [
    'image', 'img', 'photo', 'picture', 'pic',
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'
  ];
  const hasImagePattern = imageHostingPatterns.some(pattern =>
    url.toLowerCase().includes(pattern)
  );

  return hasImageExtension || hasImagePattern;
};

// Helper function to get custom file icon for specific types
const getCustomFileIcon = (mimeType?: string, fileName?: string) => {
  if (!fileName) return OtherIcon;

  const lowerFileName = fileName.toLowerCase();

  // PDF files
  if (lowerFileName.endsWith('.pdf') || mimeType === 'application/pdf') {
    return PDFIcon;
  }

  // Spreadsheet files
  if (lowerFileName.endsWith('.csv') ||
      lowerFileName.endsWith('.xlsx') ||
      lowerFileName.endsWith('.xls') ||
      mimeType?.includes('spreadsheet') ||
      mimeType?.includes('csv')) {
    return CSVIcon;
  }

  // SVG files (use CSV icon as placeholder for now)
  if (lowerFileName.endsWith('.svg') || mimeType === 'image/svg+xml') {
    return CSVIcon;
  }

  return OtherIcon;
};

// Helper function to get Lucide file icon based on mime type or extension
const getLucideFileIcon = (mimeType?: string, fileName?: string) => {
  if (mimeType?.startsWith('image/')) return Image;
  if (mimeType?.startsWith('video/')) return Video;
  if (mimeType?.startsWith('text/') || fileName?.endsWith('.txt')) return FileText;
  if (mimeType?.includes('zip') || mimeType?.includes('tar') || mimeType?.includes('rar')) return Archive;
  if (fileName?.endsWith('.pdf')) return FileText;
  if (fileName?.endsWith('.csv') || fileName?.endsWith('.xlsx')) return FileText;
  return File;
};

// Helper function to format file size
const formatFileSize = (bytes?: number) => {
  if (!bytes) return 'Unknown size';
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to truncate filename
const truncateFileName = (fileName: string, maxLength: number = 15) => {
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

  if (nameWithoutExt.length <= 4) return fileName;

  return `${nameWithoutExt.substring(0, 4)}...${extension}`;
};

/**
 * A reusable component for displaying a gallery of artifacts (images and files) from URLs
 * Used for displaying artifacts in message items
 */
function ArtifactGalleryComponent({ images, className }: ArtifactGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<ArtifactData | null>(null);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  if (!images || images.length === 0) return null;

  const handleDownload = async (artifact: ArtifactData, e: React.MouseEvent) => {
    e.stopPropagation();
    await downloadFileFromUrl(artifact.url, artifact.name || 'download');
  };

  const handleArtifactClick = (artifact: ArtifactData) => {
    if (isPreviewableImage(artifact.name, artifact.url, artifact.mime_type)) {
      setSelectedImage(artifact);
    } else {
      // For non-image files, show a message that preview is not supported
      // You could implement a custom modal here or just download the file
      console.log('Preview not supported for this file type');
    }
  };

  return (
    <>
      <div
        className={cn(
          "flex flex-wrap max-w-4xl overflow-x-auto gap-[10px] relative my-2 mb-0 rounded-lg w-full",
          className
        )}
      >
        {images.map((artifact, index) => {
          const isImage = isPreviewableImage(artifact.name, artifact.url, artifact.mime_type);
          const customIcon = getCustomFileIcon(artifact.mime_type, artifact.name);
          const LucideIcon = getLucideFileIcon(artifact.mime_type, artifact.name);

          return (
            <div
              key={index}
              className="relative rounded-[12px] p-[6px] min-h-[180px] max-h-[180px] min-w-[180px] max-w-[180px] overflow-hidden bg-[#FFFFFF0F] flex flex-col items-center justify-center cursor-pointer hover:bg-[#FFFFFF15] transition-colors"
              onClick={() => handleArtifactClick(artifact)}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              {isImage ? (
                <img
                  src={artifact.url}
                  alt={artifact.name || `Image ${index + 1}`}
                  className="object-cover w-full h-full rounded-lg"
                  onError={(e) => {
                    // Fallback to file icon if image fails to load
                    const target = e.target as HTMLImageElement;
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full p-4">
                          <img src="${customIcon}" alt="File" class="w-12 h-12 mb-2" />
                          <span class="text-[12px] text-white/70 text-center break-words">${truncateFileName(artifact.name)}</span>
                          <span class="text-[10px] text-white/50 mt-1">${formatFileSize(artifact.file_size)}</span>
                        </div>
                      `;
                    }
                  }}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full p-4">
                  {customIcon ? (
                    <img src={customIcon} alt="File" className="w-12 h-12 mb-2" />
                  ) : (
                    <LucideIcon className="w-12 h-12 mb-2 text-[#FFFFFF60]" />
                  )}
                  <span className="text-[12px] text-white/70 text-center break-words mb-1">
                    {truncateFileName(artifact.name)}
                  </span>
                  <span className="text-[10px] text-white/50">
                    {formatFileSize(artifact.file_size)}
                  </span>
                  <span className="text-[10px] text-white/40 mt-1">
                    Preview not supported
                  </span>
                </div>
              )}

              {/* Download button - appears on hover */}
              <button
                type="button"
                onClick={(e) => handleDownload(artifact, e)}
                className={`absolute top-2 right-2 p-1.5 bg-black/50 hover:bg-black/70 rounded-lg transition-opacity ${
                  hoveredIndex === index ? 'opacity-100' : 'opacity-0'
                }`}
                title="Download file"
              >
                <Download className="w-4 h-4 text-white" />
              </button>
            </div>
          );
        })}
      </div>

      {/* Image preview modal - only for images */}
      {selectedImage && isPreviewableImage(selectedImage.name, selectedImage.url, selectedImage.mime_type) && (
        <ImagePreviewUrl
          imageUrl={selectedImage.url}
          imageName={selectedImage.name}
          isOpen={!!selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </>
  );
}

// Export with both names for backward compatibility
export { ArtifactGalleryComponent as ImageGalleryUrl };
export { ArtifactGalleryComponent as ArtifactGallery };
export default ArtifactGalleryComponent;
