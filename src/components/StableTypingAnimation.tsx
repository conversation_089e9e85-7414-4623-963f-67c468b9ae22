import React, { useState, useEffect, useRef } from "react";
import { AgentTypingAnimation } from "./AgentTypingAnimation";

const StableTypingAnimation = ({ initialText, currentChunk } :{
    initialText: string;
    currentChunk: any;
}) => {
  // State for the currently displayed text
  const [displayText, setDisplayText] = useState(
    initialText || "Processing next step..."
  );
  // State for controlling the visibility of the text
  const [visible, setVisible] = useState(true);
  // Queue of messages to display
  const messageQueueRef = useRef<string[]>([]);
  // Track if we're currently processing a transition
  const isTransitioningRef = useRef(false);
  // Store the last processed chunk to detect changes
  const lastChunkRef = useRef(null);

  // Process new chunks and add messages to the queue
  useEffect(() => {
    // Skip if there's no change in the chunk
    if (
      (lastChunkRef.current &&
        JSON.stringify(lastChunkRef.current) === JSON.stringify(currentChunk))
    ) {
      return;
    }

    // Update the last processed chunk
    lastChunkRef.current = currentChunk;

    // Determine the new message based on the chunk data
    let newText = "Processing next step...";
    
    if (currentChunk && currentChunk.chunk && currentChunk.chunk.data) {
      if (currentChunk.chunk.data.reasoning_content) {
        newText = "Agent is thinking...";
      }
      if (currentChunk.chunk.data.thought) {
        newText = "Analyzing your request...";
      }
      if (currentChunk.chunk.data.tool_name === "build_mode") {
        newText = "Preparing the build environment...";
      }
      if (currentChunk.chunk.data.tool_name) {
        if (currentChunk.chunk.data.tool_name === "str_replace_editor") {
          if (currentChunk.chunk.data.arguments?.command === "create") {
            newText = "Creating a new file...";
          } else if (currentChunk.chunk.data.arguments?.command === "view") {
            newText = "Scanning File Contents...";
          } else if (
            currentChunk.chunk.data.arguments?.command === "str_replace"
          ) {
            newText = "Editing a file...";
          }
        } else if (currentChunk.chunk.data.tool_name === "bulk_file_writer") {
          const numFiles =
            currentChunk.chunk.data.arguments?.xml_content?.split("<file>") ||
            [];
          const totalFiles = numFiles.length - 1;

          newText = `Writing ${numFiles?.length > 0 ? totalFiles : "multiple"} files. Hang on...`;
        } else if (currentChunk.chunk.data.tool_name === "execute_bash") {
          newText = "Running a command in terminal...";
        } else {
          newText = "Processing next step...";
        }
      }

      if (currentChunk.chunk.data.tool_name === "build_mode") {
        newText = "Initializing cloud environment";
      }
    }

    // Only add to queue if the text is different from the current display
    if (newText !== displayText) {
      messageQueueRef.current.push(newText);

      // Start the transition process if not already in progress
      if (!isTransitioningRef.current) {
        processNextMessage();
      }
    }
  }, [currentChunk, displayText]);

  // Process the next message in the queue
  const processNextMessage = () => {
    if (messageQueueRef.current.length === 0) {
      isTransitioningRef.current = false;
      return;
    }

    isTransitioningRef.current = true;

    // Step 1: Fade out current text
    setVisible(false);

    // Step 2: After fade out, change text and fade in
    setTimeout(() => {
      const nextMessage = messageQueueRef.current.shift();
      setDisplayText(nextMessage as string);

      // Short delay before fading in the new text
      setTimeout(() => {
        setVisible(true);

        // After fade-in completes, process the next message if any
        setTimeout(() => {
          if (messageQueueRef.current.length > 0) {
            processNextMessage();
          } else {
            isTransitioningRef.current = false;
          }
        }, 1000); // Wait a second before processing next message
      }, 200);
    }, 1000); // Ensure this matches the transition duration
  };

  return (
    <div className="typing-animation-container">
      <AgentTypingAnimation text={displayText} duration={1} />
    </div>
  );
};

export default StableTypingAnimation;
