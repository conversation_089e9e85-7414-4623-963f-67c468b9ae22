
import { useState, useRef, useEffect } from "react"
import MoreIcon from "@/assets/upload/more_icon.svg"
import DeleteIcon from "@/assets/upload/delete.svg"
import { Image, Video, FileText, Archive, File } from "lucide-react"
import DownloadIcon from "@/assets/upload/download.svg"
import { ImagePreviewUrl } from "../ImagePreviewUrl"
import DownloadWhiteIcon from "@/assets/upload/download_white.svg"
import DeleteRed from "@/assets/upload/deleteActive.svg"

// Helper function to get file icon based on mime type or extension
const getFileIcon = (mimeType: string, fileName: string) => {
  if (mimeType.startsWith('image/')) return Image;
  if (mimeType.startsWith('video/')) return Video;
  if (mimeType.startsWith('text/') || fileName.endsWith('.txt')) return FileText;
  if (mimeType.includes('zip') || mimeType.includes('tar') || mimeType.includes('rar')) return Archive;
  if (fileName.endsWith('.pdf')) return FileText;
  if (fileName.endsWith('.csv') || fileName.endsWith('.xlsx')) return FileText;
  return File;
};

// Helper function to check if file type is previewable as image
const isPreviewableImage = (mimeType: string) => {
  return mimeType.startsWith('image');
};

interface IAssetCardProps {
    title: string;
    size: string;
    id: string;
    type: string;
    url: string;
    onRemove?: (id: string) => void;
    onDownload?: (id: string) => void;
}

function AssetCard({
    title,
    size,
    id,
    type,
    url,
    onRemove,
    onDownload,
}: IAssetCardProps) {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false)
    const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false)
    const dropdownRef = useRef<HTMLDivElement>(null)

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false)
            }
        }

        if (isDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside)
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [isDropdownOpen])

    const handleMoreClick = () => {
        setIsDropdownOpen(!isDropdownOpen)
    }

    const handleRemove = () => {
        onRemove?.(id)
        setIsDropdownOpen(false)
    }

    const handleDownload = () => {
        onDownload?.(id)
        setIsDropdownOpen(false)
    }

    const handleCardClick = (e: React.MouseEvent) => {
        // Don't trigger preview if clicking on the more options area
        if (dropdownRef.current && dropdownRef.current.contains(e.target as Node)) {
            return;
        }

        // For images, show preview
        if (showImagePreview) {
            setIsImagePreviewOpen(true)
        } else {
            // For non-image files, trigger download
            onDownload?.(id)
        }
    }

    // Get the appropriate icon component
    const FileIcon = getFileIcon(type, title);
    const showImagePreview = isPreviewableImage(type) && url;

    return (
        <>
            <div
                className="flex items-center justify-between gap-4 group hover:bg-[#FFFFFF0A] rounded-[12px] p-2 cursor-pointer"
                onClick={handleCardClick}
            >
                <div
                    className={`min-w-[40px] min-h-[40px] max-h-[40px] max-w-[40px] bg-[#FFFFFF0A] backdrop-blur-sm rounded-[8px] flex items-center justify-center overflow-hidden ${showImagePreview ? 'hover:bg-[#FFFFFF15] transition-colors' : ''}`}
                >
                    {showImagePreview ? (
                        <img
                            src={url}
                            alt={title}
                            className="w-full h-full object-cover rounded-[6px]"
                            onError={(e) => {
                                // Fallback to icon if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                            }}
                        />
                    ) : null}
                    <FileIcon
                        className={`w-5 h-5 text-[#FFFFFF60] ${showImagePreview ? 'hidden' : ''}`}
                    />
                </div>
            <div className="flex flex-col flex-1">
                <span className="text-[#FFFFFF] font-['Inter'] font-medium max-w-[60%] text-[16px] opacity-60 transition-all truncate duration-100 ease-in-out">{title}</span>
                <span className="text-[#FFFFFF40] font-['Inter'] font-medium text-[12px] ">{size}</span>
            </div>
            <div className="relative" ref={dropdownRef}>
                <div
                    className='hover:bg-[#FFFFFF0F] backdrop-blur-lg rounded-[8px] opacity-0 group-hover:opacity-60 hover:!opacity-100 cursor-pointer p-1 transition-opacity duration-200'
                    onClick={handleMoreClick}
                >
                    <img src={MoreIcon} alt="More" className='w-6 h-6' />
                </div>

                {isDropdownOpen && (
                    <div className="absolute right-0 overflow-clip top-8 z-50 p-[6px] bg-[#18181A] backdrop-blur-sm rounded-[12px] border border-[#242424] shadow-lg min-w-[140px]">
                        <div className="">
                            <button
                                type="button"
                                onClick={handleRemove}
                                className="flex items-center gap-2 rounded-[8px] w-full p-2 text-left text-[#808080] hover:text-[#CCCCCC] hover:bg-[#ED5B5B0F] transition-colors duration-150 group/remove"
                            >
                                <img src={DeleteIcon} alt="Remove" className="w-5 h-5 group-hover/remove:hidden" />
                                <img src={DeleteRed} alt="Remove" className="hidden w-5 h-5 group-hover/remove:block" />
                                <span className=" font-medium text-[14px] group-hover/remove:text-[#ED5B5B]">Remove</span>
                            </button>
                            <button
                                type="button"
                                onClick={handleDownload}
                                className="flex items-center gap-2 w-full p-2 rounded-[8px] py-2 text-left text-[#808080] hover:text-[#CCCCCC] hover:bg-[#FFFFFF0A] transition-colors duration-150 group/download"
                            >
                                <img src={DownloadWhiteIcon} alt="Download" className="w-5 h-5 group-hover/download:hidden opacity-40" />
                                <img src={DownloadWhiteIcon} alt="Download" className="hidden w-5 h-5 group-hover/download:block" />
                                <span className=" font-medium text-[14px]">Download</span>
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>

        {showImagePreview && (
            <ImagePreviewUrl
                imageUrl={url}
                imageName={title}
                isOpen={isImagePreviewOpen}
                onClose={() => setIsImagePreviewOpen(false)}
            />
        )}
    </>
    )
}

export default AssetCard