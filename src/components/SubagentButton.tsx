import { getSubagentName, getSubagentStyle } from "@/utils/commonUtils";
import { <PERSON><PERSON><PERSON> } from "ldrs/react";
import "ldrs/react/Tailspin.css";
import { cn } from "@/lib/utils";
import { useEffect, useRef, useCallback } from "react";
import Lottie, { LottieRefCurrentProps } from "lottie-react";
import Panel_Testing from "@/assets/panel_icons/panel_testing.json";
import ReplyIcon from "@/assets/reply.svg";
import { Loader2 } from "lucide-react";

function SubagentButton({
  handleSubagentClick,
  message,
  agentState,
  enableLoading = false,
  runningState = false,
  panelState,
  isSelected = false,
  jobDetails,
  agentName,
  onPause,
  isPauseLoading: externalIsPauseLoading,
}: {
  handleSubagentClick: () => void;
  message: any;
  agentState: any;
  enableLoading?: boolean;
  runningState?: boolean;
  panelState: any;
  isSelected?: boolean;
  jobDetails?: {
    job_id: string;
    traj_path?: string;
    container_id?: string;
    createdBy?: string;
  };
  agentName?: string;
  onPause?: ({ origin }: { origin?: "MainInput" | "SubagentButton" }) => void;
  isPauseLoading?: boolean;
}) {
  const lottieRef = useRef<LottieRefCurrentProps>(null);
  // Use external pause loading state if provided, otherwise use local state
  const isPauseLoading = externalIsPauseLoading ?? false;

  const handlePauseAgent = useCallback(async () => {
    if (!jobDetails?.job_id || !agentState?.agent_running) {
      return;
    }

    // Call the parent's pause handler
    if (onPause) {
      onPause({ origin: "MainInput" });
    }
  }, [jobDetails?.job_id, agentState?.agent_running, onPause]);

  // Control animation based on panel state and selection
  useEffect(() => {
    if (lottieRef.current) {
      const isPanelOpenForThisButton =
        isSelected && panelState.showSubagentPanel;

      if (isPanelOpenForThisButton) {
        lottieRef.current.goToAndStop(50, true);
      } else {
        lottieRef.current.play();
      }
    }
  }, [isSelected, panelState.showSubagentPanel]);

  const subagentStyle = getSubagentStyle(message.function_name);

  const isTestingAgent = message.function_name.includes("testing");

  return (
    <div className="flex md:ml-[52px] items-center gap-2 mt-2">
      {enableLoading && agentState?.agent_running ? (
        <>
          <button
            type="button"
            title="Subagent Button"
            className={cn(
              "relative p-[6px] flex items-center gap-2 rounded-[6px]",
              isSelected && panelState.showSubagentPanel && "pr-3"
            )}
            style={{
              backgroundColor: runningState
                ? subagentStyle.bgColor
                : isSelected && panelState.showSubagentPanel
                  ? "#ffffff10"
                  : "transparent",
              color: runningState ? subagentStyle.color : "#B8B8CC",
            }}
            onClick={handleSubagentClick}
          >
            {isSelected && panelState.showSubagentPanel && runningState && (
              <div
                className={cn(
                  "before:absolute before:inset-0 before:-m-[4px] before:rounded-[10px] before:border-[1.5px] ",
                  runningState
                    ? subagentStyle.beforeBorder
                    : "before:border-[#ffffff20]"
                )}
              ></div>
            )}
            <div className="flex items-center gap-1 text-nowrap">
              <Tailspin
                size="18"
                stroke="4"
                speed="0.9"
                color={subagentStyle.iconColor}
              />

              <span className=" font-brockmann font-medium text-[14px]">
                {" "}
                {getSubagentName(message.function_name)}{" "}
              </span>
            </div>

            <Lottie
              lottieRef={lottieRef}
              animationData={Panel_Testing}
              loop={true}
              autoplay={true}
              className="w-5 h-5"
            />
          </button>
        </>
      ) : (
        <>
        <button
          type="button"
          title="Subagent Button"
          className={cn(
            "relative p-[6px] flex items-center gap-2 rounded-[6px] hover:bg-[#ffffff10]",
            !(enableLoading && agentState?.agent_running) &&
              "cursor-pointer bg-red-400 hover:bg-red-600",
            isSelected && panelState.showSubagentPanel && "pr-3"
          )}
          style={{
            backgroundColor:
              isSelected && panelState.showSubagentPanel
                ? "#ffffff10"
                : "transparent",
            color: "#B8B8CC",
          }}
          onClick={handleSubagentClick}
        >
          {isSelected && panelState.showSubagentPanel && runningState && (
            <div
              className={cn(
                "before:absolute before:inset-0 before:-m-[4px] before:rounded-[10px] before:border-[1.5px] ",
                runningState
                  ? subagentStyle.beforeBorder
                  : "before:border-[#ffffff20]"
              )}
            ></div>
          )}
          <div className="flex items-center gap-1 text-nowrap">
            <img alt="Reply Icon" src={ReplyIcon} className="w-4 h-4" />
            <span className=" font-brockmann font-medium text-[14px]">
              {message?.subagent_trajectory?.length}{" "}
              {getSubagentName(message.function_name)} messages
            </span>
          </div>
        </button></>
      )}

      {enableLoading && agentState?.agent_running && isTestingAgent ? (
        <button
          type="button"
          title="Stop Subagent Button"
          className="p-[6px] flex items-center px-[8px]  gap-[6px] rounded-[6px] bg-[#FFFFFF] hover:bg-[#ffffff90] disabled:opacity-50"
          onClick={handlePauseAgent}
          disabled={isPauseLoading}
        >
          {isPauseLoading ? (
            <Loader2 className="w-4 h-4 text-black animate-spin" />
          ) : (
            <div className="w-3 h-3 bg-black rounded-sm"></div>
          )}
          <span className=" font-brockmann font-semibold text-[14px] text-[#0F0F10]">
            {isPauseLoading ? "Stopping..." : "Stop"}
          </span>
        </button>
      ) : null}
    </div>
  );
}

export default SubagentButton;
