import { useGitHub } from "../../hooks/useGitHub<PERSON>I";
import { Loader2 } from "lucide-react";
import WhiteGithubIcon from "../../assets/white-github.svg";
import MenuButton from "../ui/menu-button";
import GithubGreen from "@/assets/GithubGreen.svg";
import GreenArrowLink from "@/assets/greenLinkArrow.svg";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { agentApi } from "@/services/agentApi";
import { useEffect } from "react";

interface ConnectGitHubButtonProps {
  className?: string;
}

export function ConnectGitHubButton({ className }: ConnectGitHubButtonProps) {
  const { isConnecting, setIsConnecting, redirectToGitHubInstallation, setIsConnected, isConnected } =
    useGitHub();

  // Reset connecting state if we're connected but still showing connecting UI
  useEffect(() => {
    if (isConnected && isConnecting) {
      //console.log("Connection successful but still showing connecting UI, resetting state");
      setIsConnecting(false);
    }
  }, [isConnected, isConnecting, setIsConnecting]);

  // Define a function to directly check user details and update UI
  const checkUserDetailsAndUpdateUI = async () => {
    try {
      //console.log("Directly checking user details from API...");
      // Get user details directly from the API
      const userDetails = await agentApi.getUserDetails();
      //console.log("User details response:", userDetails);

      // Check if GitHub is authorized in the response
      if (userDetails && userDetails.github && userDetails.github.authorized === true) {
        //console.log("GitHub is authorized according to API response");

        // Explicitly update the connection state
        setIsConnected(true);

        // Show success toast
        toast({
          title: "Success",
          description: "Successfully connected to GitHub",
        });

        return true;
      } else {
        //console.log("GitHub is not authorized according to API response");
        return false;
      }
    } catch (error) {
      console.error("Error checking user details:", error);
      return false;
    } finally {
      // Always reset connecting state
      setIsConnecting(false);
    }
  };

  // Set up a polling mechanism to check for connection
  const pollForConnection = () => {
    //console.log("Starting to poll for GitHub connection...");
    let attempts = 0;
    const maxAttempts = 10;
    const pollInterval = 3000; // 3 seconds

    const pollTimer = setInterval(async () => {
      attempts++;
      //console.log(`Polling attempt ${attempts}/${maxAttempts}`);

      const connected = await checkUserDetailsAndUpdateUI();

      if (connected || attempts >= maxAttempts) {
        clearInterval(pollTimer);
        if (connected) {
          // Reload the page to reflect the new connection state
          window.location.reload();
        } else if (attempts >= maxAttempts) {
          //console.log("Max polling attempts reached without success");
          setIsConnecting(false);
        }
      }
    }, pollInterval);

    // Clear interval after a maximum time to prevent infinite polling
    setTimeout(() => {
      clearInterval(pollTimer);
      setIsConnecting(false);
    }, pollInterval * maxAttempts + 1000);
  };

  const handleConnect = async () => {
    try {
      setIsConnecting(true);

      redirectToGitHubInstallation({
        isPopup: true,
        pollingEnabled: false, // We'll handle polling ourselves
        onSuccess: async () => {
          //console.log("GitHub connection callback triggered");
          const connected = await checkUserDetailsAndUpdateUI();
          if (connected) {
            window.location.reload();
          }
        },
      });

      // Start polling for connection status
      pollForConnection();
    } catch (err) {
      console.error("Error connecting to GitHub:", err);
      setIsConnecting(false);
      toast({
        title: "Error",
        description: "Failed to connect to GitHub",
        variant: "destructive",
      });
    }
  };

  return (
    <MenuButton
      disabled={isConnecting}
      loadingNode={<Loader2 className="animate-spin" />}
      loadingState={isConnecting}
      hoveringEndIcon={GreenArrowLink}
      hoverIcon={GithubGreen}
      icon={WhiteGithubIcon}
      className={cn(" hover:bg-[#2EE5720F] w-full hover:text-[#2EE572] opacity-70 hover:opacity-100 rounded-[8px]", className)}
      onClick={handleConnect}
    >
      Connect to Github
    </MenuButton>
  );
}

export default ConnectGitHubButton;