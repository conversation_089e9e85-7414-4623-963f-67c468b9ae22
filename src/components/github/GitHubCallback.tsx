import { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGitHub } from '../../hooks/useGitHubAPI';
import { useToast } from '../../hooks/use-toast';
import { Spinner } from '../ui/spinner';
import { trackAuthEvent, trackUserOnboarding, trackUserSource } from '../../services/postHogService';
import { useAuth } from '../../contexts';

/**
 * Component to handle GitHub OAuth callback in web environment
 * Processes the installation_id and setup_action parameters from the URL
 * If opened as a popup, closes itself and refreshes the parent window
 */
export function GitHubCallback() {
  const navigate = useNavigate();
  const location = useLocation();
  const { saveInstallation, checkGitHubConnection, setIsConnected } = useGitHub();
  const { toast } = useToast();
  const { user } = useAuth();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState<string>('Processing GitHub authorization...');
  const processedRef = useRef(false);

  useEffect(() => {
    const processCallback = async () => {
      // Prevent multiple processing of the same callback
      if (processedRef.current) return;
      processedRef.current = true;

      try {
        // Parse the URL search params
        const searchParams = new URLSearchParams(location.search);
        const installationId = searchParams.get('installation_id');
        const setupAction = searchParams.get('setup_action');
        const code = searchParams.get('code') || "";

        // Check if we have the required parameters
        if (installationId && setupAction === 'install') {
          //console.log('GitHub installation successful:', { installationId, setupAction });

          // Save the installation ID
          const success = await saveInstallation(installationId, code);

          if (success) {
            // Update connection status
            await checkGitHubConnection();
            setIsConnected(true);
            setStatus('success');
            setMessage('GitHub connected successfully! Closing this window...');

            // Track feature usage
            trackUserOnboarding('profile_completed', {
              userId: user?.id,
              feature: 'github_connection',
              installationId
            });

            // Show success toast
            toast({
              title: "Success",
              description: "GitHub connected successfully",
              variant: "default"
            });

            // Check if this was from an OAuth login
            const isPendingOAuth = sessionStorage.getItem('oauth_login_pending') === 'true';
            if (isPendingOAuth) {
              const provider = sessionStorage.getItem('oauth_provider') || 'unknown';

              // Track OAuth login success
              trackAuthEvent('login_success', {
                method: provider,
                userId: user?.id,
                withGitHubConnection: true
              });

              // Check if this is the user's first login
              const isFirstLogin = localStorage.getItem('first_login_completed') !== 'true';
              if (isFirstLogin && user) {
                localStorage.setItem('first_login_completed', 'true');
                trackUserOnboarding('first_login', {
                  userId: user.id,
                  method: provider,
                  withGitHubConnection: true
                });

                // Track user source on first login
                trackUserSource({
                  isNewUser: true,
                  userId: user.id,
                  loginMethod: provider,
                  withGitHubConnection: true
                });
              }

              // Clear the pending OAuth flag
              sessionStorage.removeItem('oauth_login_pending');
              sessionStorage.removeItem('oauth_provider');
            }

            // Refresh the parent window
            if (window.opener && !window.opener.closed) {
              try {
                // Force a full reload of the parent window to ensure context is refreshed
                window.opener.location.href = window.opener.location.href;
              } catch (err) {
                console.error('Error refreshing parent window:', err);
              }
            }

            // Close this tab after a short delay
            setTimeout(() => {
              try {
                window.close();
              } catch (err) {
                console.error('Error closing window:', err);
                // If window.close() doesn't work (some browsers block it), redirect to home
                navigate('/home');
              }
            }, 2000);
          } else {
            setStatus('error');
            setMessage('Failed to save GitHub installation');
            toast({
              title: "Error",
              description: "Failed to save GitHub installation",
              variant: "destructive"
            });
          }
        } else {
          // Missing required parameters
          setStatus('error');
          setMessage('Invalid GitHub callback parameters');
          toast({
            title: "Error",
            description: "Invalid GitHub callback parameters",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error processing GitHub callback:', error);
        setStatus('error');
        setMessage('An unexpected error occurred');
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive"
        });
      }
    };

    processCallback();
  }, [location.search, navigate, saveInstallation, checkGitHubConnection, setIsConnected, toast]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-background">
      <div className="w-full max-w-md p-6 space-y-6 rounded-lg shadow-lg bg-card">
        <h2 className="text-2xl font-bold text-center">
          {status === 'processing' && 'Connecting to GitHub...'}
          {status === 'success' && 'GitHub Connected!'}
          {status === 'error' && 'Connection Failed'}
        </h2>

        <div className="flex justify-center">
          {status === 'processing' && <Spinner className="w-6 h-6" />}
          {status === 'success' && (
            <div className="p-2 bg-green-100 rounded-full">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          )}
          {status === 'error' && (
            <div className="p-2 bg-red-100 rounded-full">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          )}
        </div>

        <p className="text-center text-muted-foreground">
          {message}
        </p>

        {status === 'error' && (
          <div className="flex justify-center">
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-white rounded-md bg-primary hover:bg-primary/90"
              onClick={() => navigate('/home')}
            >
              Return to Home
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default GitHubCallback;
