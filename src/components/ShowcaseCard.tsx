import { cn } from '@/lib/utils';
import { useState } from 'react';
import { motion } from 'motion/react';
import ArrowUpRight from "@/assets/arrow-up-right.svg"

export interface IShowcaseCardProps {
    title: string;
    description: string;
    imageLink: string;
    hrefLink: string;
    className?: string;
    categories?: string[];
    job_id: string;
    handleOnClick: (id: string) => void;
}

const ShowcaseCard = ({
    title, description, imageLink, hrefLink, className, handleOnClick, job_id
}: IShowcaseCardProps) => {
    const [isHovered, setIsHovered] = useState(false);

    return (
        <motion.div
            className={cn("bg-[#18181A] rounded-2xl h-[280px] flex-1 overflow-hidden relative cursor-pointer", className)}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
            onClick={() => handleOnClick(job_id)}
            id={`showcase-card-${job_id}`}
            // Enhanced border transition
            initial={{ borderColor: 'transparent' }}
            animate={{
                borderColor: isHovered ? '#333333' : 'transparent',
                boxShadow: isHovered ? '0 0 10px rgba(51, 51, 51, 0.3)' : 'none'
            }}
            transition={{ duration: 0.2 }}
            style={{
                border: '1px solid',
            }}
        >
            <div className='relative z-10 flex flex-col gap-4 px-5 pt-8'>
                <div className='flex items-center justify-between'>
                    {/* Title with enhanced color transition on hover */}
                    <motion.span
                        className='text-lg font-medium font-brockmann'
                        initial={{ color: '#ffffff' }}
                        animate={{
                            color: isHovered ? '#66eaff' : '#ffffff',
                        }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                    >
                        {title}
                    </motion.span>
                    <motion.img
                        src={ArrowUpRight}
                        title='Open Link'
                        initial={{ opacity: 0, x: -10 }}
                        animate={{
                            opacity: isHovered ? 1 : 0,
                            x: isHovered ? 0 : -10
                        }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                    />
                </div>
                <p className='text-sm font-medium text-white opacity-30 font-inter'>
                    {description}
                </p>
            </div>

            {/* Gradient Overlay */}
            <motion.div
                className='absolute max-h-[150px] inset-0 bg-gradient-to-b from-[#0F0F1000] to-[#0F0F10] z-[15]'
                animate={{
                    opacity: isHovered ? 0.4 : 1,
                    y: isHovered ? 190 : 80
                }}
                transition={{ duration: 0.2 }}
            />

            {/* Image */}
            <motion.div
                className='absolute w-full bottom-0 z-[20] px-[10px]'
                animate={{
                    y: isHovered ? '90%' : '40%'
                }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
            >
                {/* <iframe
                    src={hrefLink}
                    className='w-full object-cover min-h-[180px] rounded-lg'
                    loading="lazy"
                    title={title}
                    frameBorder="0"
                    referrerPolicy="no-referrer"
                /> */}
                {/* Fallback for iframe issues */}
                <img
                    src={imageLink}
                    className='w-full object-cover min-h-[180px] rounded-lg'
                    alt={title}
                    loading="lazy"
                />
            </motion.div>
        </motion.div>
    );
};

export default ShowcaseCard;