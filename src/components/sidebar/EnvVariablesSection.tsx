import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import KeyPurple from "@/assets/KeyPurple.svg";
import PurpleDownArrow from "@/assets/PurpleDownArrow.svg";
import EditIconSVG from "@/assets/iconamoon_edit-fill.svg";
import CopyIconSVG from "@/assets/copyPurple.svg";

import { useToast } from "@/hooks/use-toast";
import { EnvValues } from "@/store/deploySlice";


import Eye from "@/assets/eye_new.svg";
import EyeOffIcon from "@/assets/eye_off.svg";

interface InputFieldState {
  isEditing: {
    value: boolean;
  };
  showPassword: boolean;
  editMode: boolean;
}

interface EnvVariablesSectionProps {
  envs: EnvValues[];
  onSyncStatusChange: (isSynced: boolean) => void;
  saveEnvironmentVariables?: (envs: EnvValues[]) => void;
  deployStatus?: "running" | "success" | "failed" | "loading" | "not_deployed";
  jobId?: string;
  updateEnvironmentVariables?: (envs: EnvValues[]) => Promise<any>;
}

const EnvVariablesSection = ({
  onSyncStatusChange,
  envs,
  saveEnvironmentVariables,
  deployStatus = "not_deployed",
  jobId,
  updateEnvironmentVariables,
}: EnvVariablesSectionProps) => {
  const { toast } = useToast();
  const [showEnvVariables, setShowEnvVariables] = useState(false);
  const [inputStates, setInputStates] = useState<
    Record<number, InputFieldState>
  >({});
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const handleEnvVariablesClick = () => {
    setShowEnvVariables(!showEnvVariables);
  };
  const [envValues, setEnvValues] = useState<EnvValues[]>(envs);

  useEffect(() => {
    setEnvValues(envs);
  }, [envs]);

  // Check if any environment variables have been modified
  const checkEnvVariablesSync = () => {
    const hasChanges = envValues.some((env) => env.value !== env.originalValue);
    onSyncStatusChange(!hasChanges);
  };



  const handleValueChange = (index: number, newValue: string) => {
    const updatedValues = [...envValues];
    updatedValues[index] = { ...updatedValues[index], value: newValue };
    setEnvValues(updatedValues);

    // Save changes to localStorage if saveEnvironmentVariables is provided
    if (saveEnvironmentVariables) {
      saveEnvironmentVariables(updatedValues);
    }

    checkEnvVariablesSync();
  };

  // Initialize input states for each env value
  useEffect(() => {
    const initialStates: Record<number, InputFieldState> = {};
    envValues.forEach((_, index) => {
      initialStates[index] = {
        isEditing: {
          value: false
        },
        showPassword: false,
        editMode: false,
      };
    });
    setInputStates(initialStates);
  }, [envValues.length]);

  // Check initial sync status when component mounts
  useEffect(() => {
    checkEnvVariablesSync();
  }, []);

  const handleFocus = (index: number) => {
    setInputStates((prev) => ({
      ...prev,
      [index]: {
        ...prev[index],
        isEditing: {
          value: true
        }
      },
    }));
  };

  const handleBlur = (index: number) => {
    setInputStates((prev) => ({
      ...prev,
      [index]: {
        ...prev[index],
        isEditing: {
          value: false
        }
      },
    }));
  };

  const togglePasswordVisibility = (index: number) => {
    setInputStates((prev) => ({
      ...prev,
      [index]: { ...prev[index], showPassword: !prev[index]?.showPassword },
    }));
  };

  const handleCopyValue = (index: number) => {
    navigator.clipboard.writeText(envValues[index].value);
    setCopiedIndex(index);

    // Reset copied status after 2 seconds
    setTimeout(() => {
      setCopiedIndex(null);
    }, 2000);
  };

  const toggleEditMode = async (index: number, event?: React.MouseEvent) => {
    // Prevent default form submission behavior if event is provided
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    const isCurrentlyEditing = inputStates[index]?.editMode;

    // First toggle the edit mode in the UI
    setInputStates((prev) => ({
      ...prev,
      [index]: { ...prev[index], editMode: !prev[index]?.editMode },
    }));

    // If we're exiting edit mode, save the changes
    if (isCurrentlyEditing) {
      // Make a copy of the current values to avoid reference issues
      const currentEnvValues = [...envValues];

      // Save to local state via Redux
      if (saveEnvironmentVariables) {
        saveEnvironmentVariables(currentEnvValues);
      }

      // If deployment status is "not_deployed", update environment variables immediately
      if (deployStatus === "not_deployed" && jobId && updateEnvironmentVariables) {
        try {
          // Use a local variable to store the result to avoid state issues
          const result = await updateEnvironmentVariables(currentEnvValues);

          // Only show toast if the API call was successful
          if (result) {
            toast({
              title: "Environment Variables Updated",
              description: "Your environment variables have been updated successfully.",
              duration: 2000,
            });
          }
        } catch (error) {
          console.error("Error updating environment variables:", error);
          toast({
            title: "Update Failed",
            description: "Failed to update environment variables. Please try again.",
            variant: "destructive",
            duration: 2000,
          });
        }
      }
    }

    // Check for changes after toggling edit mode
    setTimeout(() => {
      checkEnvVariablesSync();
    }, 0);
  };

  // Define animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3
      }
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.2
      }
    }
  };

  const contentVariants = {
    hidden: {
      height: 0,
      opacity: 0
    },
    visible: {
      height: "auto",
      opacity: 1,
      transition: {
        height: {
          type: "spring" as const,
          stiffness: 300,
          damping: 30
        },
        opacity: {
          duration: 0.2,
          delay: 0.1
        }
      }
    },
    exit: {
      height: 0,
      opacity: 0,
      transition: {
        height: {
          type: "spring" as const,
          stiffness: 500,
          damping: 40
        },
        opacity: {
          duration: 0.1
        }
      }
    }
  };



  return (
    <motion.div
      className=""
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <div className="flex items-center flex-col bg-[#A199FF0F] p-3 mb-3  md:p-5 md:mb-5 rounded-[12px]">
        <div
          onClick={handleEnvVariablesClick}
          className="flex flex-col w-full gap-2 cursor-pointer"
        >
          <div className="flex items-center justify-between w-full cursor-pointer">
            <div className="flex items-center gap-2">
              <img src={KeyPurple} alt="Key" className="w-6 h-6" />
              <div className="text-[#A199FF] text-[14px]  md:text-[16px] font-medium font-['Inter']">
                Env Variables
              </div>
            </div>

            <img
              onClick={handleEnvVariablesClick}
              src={PurpleDownArrow}
              alt="Down Arrow"
              className={cn(
                "w-5 h-5 md:w-6 md:h-6 opacity-50 cursor-pointer hover:opacity-100 transition-transform duration-300",
                {
                  "rotate-180": showEnvVariables,
                }
              )}
            />
          </div>

          <div className="text-[13px] md:text-[16px] font-['Inter'] text-[#B9B2FF] opacity-60">
            Secrets and environment variables are confidential credentials that enable secure access to APIs and services. 
            Carefully handle secrets to avoid application failure.
          </div>
        </div>

        <div className="w-full overflow-hidden">
          <AnimatePresence>
            {showEnvVariables && (
              <motion.div
                className="w-full"
                variants={contentVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                {envValues.map((envItem, index) => (
                  <div
                    key={index}
                    className="flex flex-col items-center justify-between w-full gap-2 md:gap-4 max-md:p-2 mt-4 max-md:border border-[#B9B2FF25] max-md:rounded-[10px] md:flex-row"
                  >
                    <div className="flex flex-col items-center w-full gap-2 md:gap-4 md:flex-row">
                      <div
                        className={cn(
                          "w-full border rounded-[8px] transition-all duration-200",
                          "bg-[#A199FF14] border-transparent"
                        )}
                      >
                        <input
                          type="text"
                          value={envItem.key}
                          placeholder="Key Name"
                          readOnly={true}
                          className={cn(
                            "w-full py-[10px] px-[14px] bg-transparent focus:outline-none placeholder-[#626266] transition-colors duration-200 font-[inter] text-[14px]",
                            "text-[#B9B2FF] cursor-not-allowed"
                          )}
                        />
                      </div>

                      <div
                        className={cn(
                          "pr-[8px] justify-between flex w-full border rounded-[8px] items-center transition-all duration-200",
                          inputStates[index]?.editMode
                            ? cn(
                                "bg-[#FFFFFF0A]",
                                inputStates[index]?.isEditing?.value
                                  ? "border-[#fff]/80"
                                  : "border-[#ffffff]/30"
                              )
                            : "bg-[#A199FF14] border-transparent"
                        )}
                      >
                        <input
                          type={
                            inputStates[index]?.showPassword
                              ? "text"
                              : "password"
                          }
                          value={envItem.value}
                          placeholder="Key Value"
                          onChange={(e) =>
                            handleValueChange(index, e.target.value)
                          }
                          onFocus={() => handleFocus(index)}
                          onBlur={() => handleBlur(index)}
                          readOnly={!inputStates[index]?.editMode}
                          className={cn(
                            "py-[10px] px-[14px] bg-transparent placeholder-[#626266] focus:outline-none transition-colors duration-200 font-[inter] text-[14px]",
                            inputStates[index]?.editMode
                              ? inputStates[index]?.isEditing?.value
                                ? "text-white"
                                : "text-[#E6E6E6]"
                              : "text-[#B9B2FF]",
                            !inputStates[index]?.editMode &&
                              "cursor-not-allowed"
                          )}
                        />
                        <div
                          role="button"
                          tabIndex={0}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            togglePasswordVisibility(index);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              e.stopPropagation();
                              togglePasswordVisibility(index);
                            }
                          }}
                          className="text-[#626266] hover:text-[#B9B2FF] transition-colors w-8 h-8 flex items-center justify-center rounded-md hover:bg-white/5 cursor-pointer"
                        >
                          {inputStates[index]?.showPassword ? (
                            <img src={EyeOffIcon} alt="Hide" className="w-6 h-6" />
                          ) : (
                            <img src={Eye} alt="Show" className="w-6 h-6" />
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 max-md:w-full md:min-w-[72px]">
                      {inputStates[index]?.editMode ? (
                        <div
                          role="button"
                          tabIndex={0}
                          title="Save"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            // Use setTimeout to ensure this runs after the current event loop
                            setTimeout(() => {
                              toggleEditMode(index, e);
                            }, 0);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              e.stopPropagation();
                              // Use setTimeout to ensure this runs after the current event loop
                              setTimeout(() => {
                                toggleEditMode(index, e as unknown as React.MouseEvent);
                              }, 0);
                            }
                          }}
                          className="relative px-3  flex items-center justify-center bg-[#2EE572] max-md:w-full min-h-[40px] rounded-[10px] cursor-pointer"
                        >
                          {/* <img
                            src={GreenTick}
                            alt="Save"
                            className="w-[30px] h-[30px]"
                          /> */}
                          <span className="text-[#0f0f10] font-semibold">Save</span>
                        </div>
                      ) : (
                        <>
                          <div
                            role="button"
                            tabIndex={0}
                            title="Edit"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              // Use setTimeout to ensure this runs after the current event loop
                              setTimeout(() => {
                                toggleEditMode(index, e);
                              }, 0);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                e.stopPropagation();
                                // Use setTimeout to ensure this runs after the current event loop
                                setTimeout(() => {
                                  toggleEditMode(index, e as unknown as React.MouseEvent);
                                }, 0);
                              }
                            }}
                            className="relative flex gap-2 items-center justify-center max-md:w-full cursor-pointer max-md:bg-[#A199FF25] rounded-[10px] p-1 px-3 hover:bg-[#A199FF14]"
                          >
                            <img
                              src={EditIconSVG}
                              alt="Edit"
                              className="w-6 h-6 md:w-7 md:h-7"
                            />
                            <span className="text-[#B9B2FF90] md:hidden font-semibold">Edit</span>
                          </div>

                          <div
                            role="button"
                            tabIndex={0}
                            title="Copy"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleCopyValue(index);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                e.stopPropagation();
                                handleCopyValue(index);
                              }
                            }}
                            className="relative flex gap-2 text-[#A199FF] items-center justify-center max-md:w-full cursor-pointer max-md:bg-[#A199FF25] rounded-[10px] p-1 px-3  hover:bg-[#A199FF14]"
                          >
                            <img
                              src={CopyIconSVG}
                              alt="Copy"
                              className="w-6 h-6 md:w-7 md:h-7"
                            />
                            <span className="text-[#B9B2FF90] md:hidden font-semibold">{copiedIndex === index ? "Copied!" : "Copy"}</span>
                            {copiedIndex === index && (
                              <span className="absolute hidden md:block -top-8 left-1/2 transform -translate-x-1/2 bg-[#333333] text-white text-xs px-2 py-1 rounded">
                                Copied!
                              </span>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};

export default EnvVariablesSection;
