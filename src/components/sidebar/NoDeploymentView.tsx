import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import WebImage from "@/assets/deployment/webImage.svg";
import DeployArrowDark from "@/assets/arrow_upload_ready_dark.svg";
import {
  Tooltip,
  TooltipArrow,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { cn } from "@/lib/utils";

interface NoDeploymentViewProps {
  onDeployClick: () => void;
  loading: boolean;
  localLoading: boolean;
  podIsPaused?: boolean;
}

export function NoDeploymentView({
  onDeployClick,
  loading,
  localLoading,
  podIsPaused
}: NoDeploymentViewProps) {
  return (
    <motion.div
      className="p-3 md:p-6 md:pb-5"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
      }}
    >
      <div className="mb-4 text-[14px] md:text-[16px] md:mb-6 font-['Inter'] text-[#939399]">
        Deploy your application to an Emergent hosted
        production-ready environment. This will make it publicly
        accessible with managed infrastructure that never sleeps.
      </div>

      <div className="flex flex-col items-center justify-center p-4 bg-[#131314] rounded-xl border border-[#242424] py-[24px] md:gap-8 md:py-[48px]">
        <div className="flex flex-col items-center">
          <img
            src={WebImage}
            alt="Web"
            className="w-10 h-10 mb-2 md:w-12 md:h-12 md:mb-4"
          />
          <div className="mb-3 text-[14px]  md:text-lg font-medium font-['Inter'] text-white">
            Start New Deployment
          </div>
          <div className="flex flex-col mb-3 md:mb-6 text-center font-medium text-[13px]  md:text-[15px] text-white/50">
            <span className="md:block hidden font-['Inter']">
              Ready to go live? Hit Start Deployment to launch your
              app.
            </span>
            <span className="font-['Inter'] text-[#F3CA5F] ">
              Each new deployment costs 50 credits/month.
            </span>
          </div>

          <div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <button
                    type="button"
                    onClick={onDeployClick}
                    className="flex items-center gap-2 px-3 py-1 text-black bg-white rounded-full md:px-6 md:py-2 hover:bg-white/90 disabled:opacity-50"
                    disabled={loading || localLoading || podIsPaused}
                  >
                    {loading || localLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        <span>Loading...</span>
                      </>
                    ) : (
                      <div className="flex items-center gap-2 text-[15px]  md:text-lg font-semibold font-brockmann">
                        <span>Start Deployment</span>
                        <img
                          src={DeployArrowDark}
                          alt="Deploy"
                          className="w-4 h-4 md:w-6 md:h-6"
                        />
                      </div>
                    )}
                  </button>
                </TooltipTrigger>

                <TooltipContent
                  className={cn(
                    "bg-[#DDDDE6] text-black border-0",
                    !podIsPaused ? "hidden" : ""
                  )}
                >
                  <span className="text-[12px] md:text-[14px] font-['Inter']">
                    {podIsPaused ? "Please wake up the agent before deploying" : ""}
                  </span>
                  <TooltipArrow className="fill-[#DDDDE6]" />
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
