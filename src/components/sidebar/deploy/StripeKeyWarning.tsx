import KeyAlert from "@/assets/deployment/key_alert.svg";
import <PERSON><PERSON> from "@/assets/deployment/stripe.svg";

import Eye from "@/assets/eye_new.svg";
import EyeOffIcon from "@/assets/eye_off.svg";
import AlertOrange from "@/assets/alert-orange.svg"

import { useEffect, useState } from "react";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface StripeKeyWarningProps {
  value: {
    containsStripeKey: boolean;
    stripeKeyValue: string;
    stripeKeyId: string;
    stripeKey: string;
  };
  onStripeKeyUpdate?: (keyId: string, newValue: string) => void;
  termsSelected: boolean;
  setTermsSelected: (value: boolean) => void;
}

const StripeKeyWarning = ({value, onStripeKeyUpdate, termsSelected, setTermsSelected}: StripeKeyWarningProps) => {

    const [showPassword, setShowPassword] = useState(false);

    const [isProdKey, setIsProdKey] = useState(false);
    const [stripeValue, setStripeValue] = useState<{
        value: string;
        keyId: string;
        originalValue: string;
    }>({
        value: value.stripeKeyValue,
        keyId: value.stripeKey,
        originalValue: value.stripeKeyValue,
    });

    // Update local state when props change
    useEffect(() => {
        setStripeValue({
            value: value.stripeKeyValue,
            keyId: value.stripeKey,
            originalValue: value.stripeKeyValue,
        });
    }, [value]);

    useEffect(() => {
        if (stripeValue.value.includes("sk_live_") || stripeValue.value.includes("sk_prod_")) {
            setIsProdKey(true);
        } else {
            setIsProdKey(false);
        }
    }, [stripeValue.value]);

    // Handle stripe key value change
    const handleStripeKeyChange = (newValue: string) => {
        setStripeValue({
            ...stripeValue,
            value: newValue,
        });

        // Call the callback to update parent component
        if (onStripeKeyUpdate) {
            onStripeKeyUpdate(stripeValue.keyId, newValue);
        }
    };

 if (!value.containsStripeKey) {
    return null;
  }

  return (
    <div className="flex flex-col bg-[#F2994A20] rounded-[10px] overflow-clip">
      <div className="flex flex-col gap-2 bg-[#242424] rounded-[10px] border-[#8B8B8B20] border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <img src={KeyAlert} alt="Key Alert" className="w-6 h-6" />
            <span className="text-[#C5C5C5] font-['Inter'] font-medium leading-[20px]">
              Stripe Integration Detected
            </span>
          </div>
          <img src={Stripe} alt="Stripe" className="w-[15px] h-[20px]" />
        </div>
        <p className="text-[#6C6C6C] font-medium text-[14px] font-['Inter'] pb-1">
          {isProdKey ? (
            <span>
              We found a{" "}
              <span className="text-[#A199FF]  font-semibold tracking-wide">
                Live Stripe Key
              </span>{" "}
              in your app.
            </span>
          ) : (
            <>
              We found a{" "}
              <strong className="text-[#A199FF] tracking-wide">
                Test Stripe Key
              </strong>{" "}
              in your app. Your deployment will work perfectly for testing, but
              to accept real payments from customers, please provide your Live
              Stripe API key.
              <br />
              <br />
              You can update the key anytime and redeploy for free.{" "}
            </>
          )}
        </p>
        <div className="bg-[#323232] rounded-[8px] px-4 py-[10px]">
          <input
            type="text"
            disabled
            value={stripeValue.keyId}
            placeholder="Environment variable name"
            className="w-full rounded-md border border-none font-semibold font-['Inter'] focus:border-none focus:outline-none bg-transparent placeholder-[#808080] text-[#808080]"
          />
        </div>
        <div className="bg-[#323232] relative rounded-[8px] px-4 py-[10px]">
          <input
            type={showPassword ? "text" : "password"}
            value={stripeValue.value}
            onChange={(e) => handleStripeKeyChange(e.target.value)}
            placeholder={
              !isProdKey ? "Enter your live Stripe API key" : "Live key detected"
            }
            onFocus={() => setShowPassword(true)}
            onBlur={() => setShowPassword(false)}
            className={cn(" w-full rounded-md border border-none font-semibold font-['Inter'] focus:border-none focus:outline-none bg-transparent placeholder-[#808080] text-[#808080]", showPassword ? "text-white" : "text-[#808080]") }
          />
          <img
            src={showPassword ? EyeOffIcon : Eye}
            alt="Eye"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute w-6 h-6 -translate-y-1/2 cursor-pointer grayscale right-3 top-1/2"
          />
        </div>
      </div>

      <div className="p-2 md:p-4 md:pt-[14px] rounded-[10px] rounded-t-none flex flex-col gap-2">
        <div className="flex items-start justify-start gap-1">
          {!isProdKey ? (
            <img
              src={AlertOrange}
              alt="Replace"
              className="inline-block w-5 h-5 mt-1 mr-2"
            />
          ) : (
            <div
              title="I acknowledge that I have tested my Stripe integration and understand the risks of deploying with a live API key."
              onClick={() => setTermsSelected(!termsSelected)}
              className={`min-w-5 min-h-5 mt-1 mr-2 border-[2px] border-[#E58A5C] rounded cursor-pointer flex items-center justify-center ${
                termsSelected ? 'bg-[#E58A5C]' : 'bg-[#E58A5C40]'
              }`}
            >
              {termsSelected && (
                <Check className="w-4 h-4 text-white" />
              )}
            </div>
          )}
          <p className="text-[#E58A5C] font-['Inter'] text-[14px]">
            <span className="font-semibold text-[#E58A5C] font-['Inter'] text-[14px]">
              {" "}
            Disclaimer:
            </span>{" "}
            Ensure your Stripe API Key is valid and you have tested the
            application yourself. Emergent is not liable for any third-party
            transactions or any losses.
          </p>
        </div>
      </div>
    </div>
  );
};

export default StripeKeyWarning;
