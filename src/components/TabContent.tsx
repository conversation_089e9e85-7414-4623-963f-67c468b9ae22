import React from 'react';
import { Provider } from 'react-redux';
import { createStore } from '@/store/createStore';
import Home from '@/features/home/<USER>';
import { ChatScreen } from '@/features/chat/ChatScreen';

const getChatIdFromPath = (path: string) => {
  const pathParts = path.split('/');
  return pathParts.length >= 3 ? pathParts[2] : null;
};

interface TabContentProps {
  path: string;
  tabId: string;
}

const storeCache = new Map();

// Function to clean up unused stores
export const cleanupUnusedStores = (activeTabIds: Set<string>) => {
  // Get all tab IDs in the store cache
  const cachedTabIds = Array.from(storeCache.keys());

  // Find tab IDs that are no longer active
  cachedTabIds.forEach(tabId => {
    // Always keep the home tab's store
    if (tabId !== 'home' && !activeTabIds.has(tabId)) {
      storeCache.delete(tabId);
    }
  });
};

export const TabContent: React.FC<TabContentProps> = React.memo(({ path, tabId }) => {
  // Check if the tabId is valid
  const store = React.useMemo(() => {
    if (!storeCache.has(tabId)) {
      storeCache.set(tabId, createStore());
    }
    return storeCache.get(tabId);
  }, [tabId]);



  const Content = React.useMemo(() => {
    const pathParts = path.split('/');
    const pathBase = `/${pathParts[1]}`;

    switch (pathBase) {
      case '/':
        return Home;
      case '/chat':
        const chatId = getChatIdFromPath(path) || tabId;
        return () => <ChatScreen tabId={chatId} />;
      default:
        return Home;
    }
  }, [path, tabId]);

  return (
    <Provider store={store}>
      <Content />
    </Provider>
  );
})