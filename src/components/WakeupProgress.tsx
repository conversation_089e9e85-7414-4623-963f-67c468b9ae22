import { useEffect, useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BoxLoader } from '@/components/ui/box-loader';
import { TaskList } from '@/components/ui/task-list';

interface WakeupProgressProps {
  isVisible: boolean;
  onComplete: () => void;
  apiCallComplete?: boolean;
}

// Define task status type
type TaskStatus = "pending" | "running" | "done";

// Define task type
interface Task {
  id: string;
  label: string;
  status: TaskStatus;
}

// Define the tasks for waking up the agent
const wakeupTasks: Task[] = [
  {
    id: "initialize",
    label: "Initializing agent environment",
    status: "pending",
  },
  {
    id: "resources",
    label: "Allocating resources",
    status: "pending",
  },
  {
    id: "loading",
    label: "Loading agent state",
    status: "pending",
  },
];

// Animation controller to manage all timers and state
class ProgressAnimationController {
  private timers: Set<NodeJS.Timeout> = new Set();
  private animationFrames: Set<number> = new Set();
  private isDestroyed = false;

  setTimeout(callback: () => void, delay: number): NodeJS.Timeout {
    if (this.isDestroyed) return setTimeout(() => {}, 0);

    const timer = setTimeout(() => {
      this.timers.delete(timer);
      if (!this.isDestroyed) callback();
    }, delay);

    this.timers.add(timer);
    return timer;
  }

  requestAnimationFrame(callback: () => void): number {
    if (this.isDestroyed) return 0;

    const frame = requestAnimationFrame(() => {
      this.animationFrames.delete(frame);
      if (!this.isDestroyed) callback();
    });

    this.animationFrames.add(frame);
    return frame;
  }

  cleanup() {
    this.isDestroyed = true;
    this.timers.forEach(timer => clearTimeout(timer));
    this.animationFrames.forEach(frame => cancelAnimationFrame(frame));
    this.timers.clear();
    this.animationFrames.clear();
  }

  isActive() {
    return !this.isDestroyed;
  }
}

export function WakeupProgress({ isVisible, onComplete, apiCallComplete = false }: WakeupProgressProps) {
  const [tasks, setTasks] = useState<Task[]>(wakeupTasks);
  const [progress, setProgress] = useState(0);
  const animationController = useRef<ProgressAnimationController | null>(null);
  const currentStepIndex = useRef<number>(0);
  const isCompleted = useRef<boolean>(false);

  // Update task status
  const updateTaskStatus = useCallback((taskId: string, status: TaskStatus) => {
    setTasks((current) => {
      const updatedTasks = current.map((task) =>
        task.id === taskId ? { ...task, status } : task
      );
      return updatedTasks;
    });
  }, []);

  // Initialize animation controller
  const initializeController = useCallback(() => {
    if (animationController.current) {
      animationController.current.cleanup();
    }
    animationController.current = new ProgressAnimationController();
  }, []);

  // Simulate progress with natural, non-robotic increments
  const simulateProgress = useCallback(() => {
    if (!isVisible || isCompleted.current) return;

    // Initialize controller if not exists
    if (!animationController.current) {
      initializeController();
    }

    const controller = animationController.current!;

    // Define step type
    interface ProgressStep {
      id: string;
      progressRange: [number, number];
      duration: [number, number];
    }

    // Define steps with consistent timing (removed randomness for reliability)
    const steps: ProgressStep[] = [
      {
        id: "initialize",
        progressRange: [0.05, 0.35],
        duration: [1500, 2000],
      },
      {
        id: "resources",
        progressRange: [0.35, 0.70],
        duration: [1800, 2200],
      },
      {
        id: "loading",
        progressRange: [0.70, 0.92], // Stop at 92% - final 8% after API call
        duration: [1600, 2000],
      }
    ];

    // Simplified easing function without randomness
    const smoothEase = (progress: number): number => {
      // Simple ease-out curve
      return 1 - Math.pow(1 - progress, 2);
    };

    const startStep = () => {
      if (!controller.isActive() || currentStepIndex.current >= steps.length) {
        if (controller.isActive()) {
          // All steps visually completed, waiting for API response
          updateTaskStatus("loading", "running");
          onComplete();
        }
        return;
      }

      const currentStep = steps[currentStepIndex.current];
      const [startProgress, endProgress] = currentStep.progressRange;
      const [minDuration, maxDuration] = currentStep.duration;

      // Use consistent duration (removed randomness)
      const stepDuration = Math.floor((minDuration + maxDuration) / 2);

      // Mark current task as running
      updateTaskStatus(currentStep.id, "running");

      // Start time for this step
      const stepStartTime = Date.now();
      const totalUpdates = 20; // Fixed number of updates for consistency

      let currentUpdate = 0;

      const updateStepProgress = () => {
        if (!controller.isActive() || !isVisible || isCompleted.current) return;

        const elapsed = Date.now() - stepStartTime;
        const elapsedRatio = Math.min(elapsed / stepDuration, 1);

        if (elapsedRatio >= 1) {
          // Step completed
          setProgress(endProgress);
          updateTaskStatus(currentStep.id, "done");
          currentStepIndex.current += 1;

          // Move to next step with consistent delay
          controller.setTimeout(startStep, 500);
          return;
        }

        // Smooth progress updates
        if (currentUpdate < totalUpdates) {
          currentUpdate++;
          const progressRatio = currentUpdate / totalUpdates;
          const easedProgress = startProgress + (endProgress - startProgress) * smoothEase(progressRatio);
          setProgress(easedProgress);
        }

        // Schedule next update
        const updateInterval = stepDuration / totalUpdates;
        controller.setTimeout(updateStepProgress, updateInterval);
      };

      // Start the step
      updateStepProgress();
    };

    // Start the first step
    startStep();

    // Return cleanup function
    return () => {
      if (controller.isActive()) {
        controller.cleanup();
      }
    };
  }, [isVisible, onComplete, updateTaskStatus, initializeController]);

  // Complete the progress animation when API call is successful
  const completeProgress = useCallback(() => {
    // Mark as completed to prevent other animations
    isCompleted.current = true;

    // Clean up existing controller
    if (animationController.current) {
      animationController.current.cleanup();
    }

    // Create new controller for final animation
    const finalController = new ProgressAnimationController();

    // Animate to 100% with smooth easing
    const startValue = progress;
    const endValue = 1.0;
    const duration = 1000; // Fixed duration for consistency
    const startTime = Date.now();

    // Mark all tasks as done
    wakeupTasks.forEach(task => {
      updateTaskStatus(task.id, "done");
    });

    // Animate the final progress with a smooth curve
    const animateFinalProgress = () => {
      if (!finalController.isActive()) return;

      const elapsed = Date.now() - startTime;
      const progressRatio = Math.min(elapsed / duration, 1);

      // Use easeOutQuad for smooth finish
      const easeOutQuad = 1 - (1 - progressRatio) * (1 - progressRatio);
      const newProgress = startValue + (endValue - startValue) * easeOutQuad;

      setProgress(newProgress);

      if (progressRatio < 1) {
        finalController.requestAnimationFrame(animateFinalProgress);
      } else {
        // Clean up final controller
        finalController.cleanup();
      }
    };

    // Start the animation
    finalController.requestAnimationFrame(animateFinalProgress);
  }, [progress, updateTaskStatus]);

  // Watch for API call completion
  useEffect(() => {
    if (apiCallComplete && isVisible && !isCompleted.current) {
      completeProgress();
    }
  }, [apiCallComplete, isVisible, completeProgress]);

  // Start simulation when component becomes visible
  useEffect(() => {
    if (isVisible && !isCompleted.current) {
      // Reset state when becoming visible
      setTasks(wakeupTasks.map(task => ({ ...task, status: "pending" as TaskStatus })));
      setProgress(0);
      currentStepIndex.current = 0;
      isCompleted.current = false;

      // Clean up any existing controller
      if (animationController.current) {
        animationController.current.cleanup();
        animationController.current = null;
      }

      // Start the simulation
      simulateProgress();
    }

    // Cleanup when component unmounts or becomes invisible
    return () => {
      if (animationController.current) {
        animationController.current.cleanup();
        animationController.current = null;
      }
    };
  }, [isVisible, simulateProgress]);

  return (
    <AnimatePresence>
      
      {isVisible && (
         <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="flex flex-col items-center justify-center w-full max-w-lg min-h-screen p-4 mx-auto my-auto space-y-4 md:space-y-10">
          <div className="w-full space-y-1 text-left md:space-y-3 md:max-w-lg">
            <h1 className="font-sans text-[18px]  md:text-[32px] font-medium leading-[40px] tracking-[-0.64px] text-loader">
              Waking up the Agent
            </h1>
            <p className="font-berkeley text-[14px]  md:text-base text-[#DDDDE6]/50 text-left">
              // This usually takes a few seconds
            </p>
          </div>

          <div className="w-full max-w-lg mt-1 space-y-4 md:space-y-10 md:mt-10">
            <BoxLoader
              boxWidth={30}
              gapWidth={8}
              progress={progress}
              className="w-full"
              minBoxes={10}
              debug={false}
            />
            <TaskList tasks={tasks} className="w-full" />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
    
  );
}
