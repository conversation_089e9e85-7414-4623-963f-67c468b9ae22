import { motion } from "framer-motion";
import PulseDot from "../PulseDot";
import { cn } from "@/lib/utils";
// @ts-ignore
import animatedSpinner from "@/assets/animated-spinner.gif";
import CaptiveImage from "@/assets/captive_portal.svg";
import ReplaceIcon from "@/assets/deployment/replace.svg";
import DeployTime from "@/assets/deployment/time_grey_deployed.svg";

import ArrowLeft from "@/assets/deployment/GoBack.svg";

import { getDeploymentUrl, getTimeAgo } from "../DeployedCard";

interface IDeployment {
  id: string;
  app_name: string;
  deployStatus: "running" | "success" | "failed" | "pending";
  deployUrl: string;
  custom_domain: string;
  latest_run: {
    status: "running" | "success" | "failed" | "pending";
    updated_at: string;
  };
  description: string;
  deployment_id: string;
}

interface DeployedCardProps {
  deployment: IDeployment;
  index: number;
  hoveredCard: string | null;
  onHoverStart: (id: string) => void;
  onHoverEnd: () => void;
  onClick: (deployment: IDeployment) => void;
  fromRedeployModal?: boolean;
  onReplaceClick?: (deployment: IDeployment) => void;
  allowHover?: boolean;
}

// Function to get status indicator color
const getStatusIndicator = (status: string) => {
  if (status === "running") {
    return {
      bgColor: "#1588FC20",
      color: "#33DDFF",
      text: "Deploying",
      animate: true,
    };
  } else if (status === "success") {
    return {
      bgColor: "#2EE57220",
      color: "#2EE572",
      text: "Live",
      animate: true,
    };
  } else if (status === "failed") {
    return {
      bgColor: "#ffffff10",
      color: "#ED5B5B",
      text: "Failed",
      animate: false,
    };
  } else {
    return {
      bgColor: "#73778020",
      color: "#737780",
      text: "Unknown",
      animate: false,
    };
  }
};

export default function ReplacementCard({
  deployment,
  index,
  hoveredCard,
  onHoverStart,
  onHoverEnd,
  onClick,
  fromRedeployModal = false,
  allowHover = true,
}: DeployedCardProps) {
  const status = deployment.deployStatus;
  const statusInfo = getStatusIndicator(status);
  const deployUrl = deployment.deployUrl;
  const shortId = deployment.id.substring(0, 6);
  const deploymentTime = new Date(deployment.latest_run.updated_at);
  const timeAgo =
    deploymentTime != null
      ? `deployed ${getTimeAgo(deploymentTime)}`
      : "Unknown";

  return (
    <motion.div
      key={deployment.id}
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      whileHover={
        allowHover
          ? {
              y: -4,
              boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.2)",
              transition: {
                type: "tween",
                ease: "easeInOut",
                duration: 0.3,
              },
            }
          : {}
      }
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.2,
        delay: index * 0.1,
      }}
      onHoverStart={() => onHoverStart(deployment.id)}
      onHoverEnd={onHoverEnd}
      onClick={() => onClick(deployment)}
    >
      <div
        className={cn(
          "rounded-[16px] overflow-clip p-5 gap-5 bg-[#18181A] border h-full flex flex-col justify-between cursor-pointer transition-all duration-200",
          hoveredCard === deployment.id
            ? "border-[#FFFFFF20] shadow-lg"
            : "border-[#FFFFFF0F]",
          !fromRedeployModal ? "cursor-pointer " : "bg-[#161617]"
        )}
      >
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-4">
              <div
                className={cn(
                  "text-[14px]  md:text-[16px] font-medium font-brockmann transition-colors duration-200 capitalize",
                  hoveredCard === deployment.id
                    ? "text-white/95"
                    : "text-white/80"
                )}
              >
                {deployment.app_name.replaceAll("-", " ")}
              </div>
              <div
                className={cn(
                  "flex items-center gap-[6px] px-[8px] py-[2px] rounded-[8px] transition-all duration-200 bg-[#2EE5720A]"
                )}
              >
                {status != "running" && (
                  <PulseDot
                    color={status != "failed" ? statusInfo.color : "#ffffff60"}
                    size={16}
                    innerSize={8}
                    animate={statusInfo.animate}
                  />
                )}

                {status === "running" && (
                  <img
                    src={animatedSpinner}
                    alt="Loading..."
                    className="w-6 h-6"
                  />
                )}
                <span
                  className={cn(
                    "text-[14px] font-medium transition-colors duration-200"
                  )}
                  style={{
                    color: status != "failed" ? statusInfo.color : "#ffffff60",
                    opacity: hoveredCard === deployment.id ? 1 : 0.9,
                  }}
                >
                  {statusInfo.text}
                </span>
              </div>
            </div>
            <div
              className={cn(
                "text-[12px] md:text-[14px]  font-['Inter'] font-medium transition-colors duration-200 truncate line-clamp-2",
                hoveredCard === deployment.id
                  ? "text-white/40"
                  : "text-white/20"
              )}
            >
              {deployment.description}
            </div>
          </div>

          {status != "failed" && (
            <a
              href={getDeploymentUrl({ status, deployment, deployUrl })}
              target="_blank"
              rel="noopener noreferrer"
              className={cn(
                "flex w-full p-1 px-[10px] pl-[14px] max-h-[40px] max-w-[100px] items-center justify-center gap-2  rounded-[10px] transition-all duration-200",
                hoveredCard === deployment.id && status !== "running"
                  ? "bg-[#FFFFFF15]"
                  : "bg-[#FFFFFF0F]",
                hoveredCard === deployment.id && status !== "running"
                  ? "hover:bg-[#ffffff20]"
                  : "hover:bg-[#3A3A3C]",
                status === "running" &&
                  "opacity-30 cursor-not-allowed hover:bg-[#FFFFFF0F]"
              )}
              onClick={(e) => e.stopPropagation()}
            >
              <span
                className={cn(
                  "transition-colors duration-200",
                  hoveredCard === deployment.id
                    ? "text-white/90"
                    : "text-white/70"
                )}
              >
                Visit
              </span>
              <img src={CaptiveImage} alt="Visit" className="w-4 h-4" />
            </a>
          )}
        </div>

        <div className="flex justify-between font-['Inter'] font-medium text-[12px]  md:text-[14px] text-[#FFFFFF33]">
            <span className="font-['Inter']">Last {timeAgo}</span>
            <span className="font-['Inter']"># EMT-{shortId}</span>
        </div>
      </div>
    </motion.div>
  );
}

export type { IDeployment };
export { getStatusIndicator, getTimeAgo, getDeploymentUrl };
