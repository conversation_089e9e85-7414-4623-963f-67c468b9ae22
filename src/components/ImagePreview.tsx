import { ResponseImageData } from "@/types/message";
import { X, Download } from "lucide-react";
import { useEffect } from "react";
import { createPortal } from "react-dom";
import { downloadFileFromDataUrl } from "@/lib/utils/downloadFile";

interface ImagePreviewProps {
  image: ResponseImageData | null;
  isOpen: boolean;
  onClose: () => void;
}

/**
 * A full-screen image preview component with download functionality
 * Opens when clicking on any image in the gallery
 */
export function ImagePreview({ image, isOpen, onClose }: ImagePreviewProps) {
  // Handle escape key to close preview
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when preview is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleDownload = () => {
    if (!image) return;

    const dataUrl = `data:${image.mime_type};base64,${image.img_base64}`;
    const extension = image.mime_type.split("/")[1] || "png";
    const filename = `image-${new Date().getTime()}.${extension}`;

    downloadFileFromDataUrl(dataUrl, filename);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Only close if clicking on the backdrop, not the image
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen || !image) return null;

  return createPortal(
    <div
      className="fixed inset-0 z-[999] flex items-center justify-center bg-black/80 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      {/* Header with controls */}
      <div className="absolute top-0 left-0 right-0 z-10 flex items-center justify-between p-4 bg-gradient-to-b from-black/50 to-transparent">
        <div className="text-sm text-white/70">Image Preview</div>
        <div className="flex items-center gap-2">
          <button
            type="button"
            onClick={handleDownload}
            className="p-2 transition-all duration-200 rounded-lg text-white/70 hover:text-white bg-white/10 hover:bg-white/20"
            title="Download image"
          >
            <Download className="w-5 h-5" />
          </button>
          <button
            type="button"
            onClick={onClose}
            className="p-2 transition-all duration-200 rounded-lg text-white/70 hover:text-white bg-white/10 hover:bg-white/20"
            title="Close preview"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Image container */}
      <div className="relative flex items-center justify-center w-full h-full p-8 pt-20">
        <img
          src={`data:${image.mime_type};base64,${image.img_base64}`}
          alt="Full size preview"
          className="object-contain max-w-full max-h-full rounded-lg shadow-2xl"
          onClick={(e) => e.stopPropagation()} // Prevent closing when clicking on image
        />
      </div>
    </div>,
    document.body
  );
}
