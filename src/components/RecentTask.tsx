import { Button } from './ui/button'
import { Plus, RotateCw } from 'lucide-react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { useIsEmergentUser } from '@/hooks/useIsEmergentUser';
import { useState } from 'react';
import DeployedApps from './DeployedApps';
import { cn } from '@/lib/utils';

import GlobalWhite from "@/assets/globe_white.svg"
import GlobalActive from "@/assets/globe_active.svg"
import RecentTask from "@/assets/task.svg"
import { Job } from '@/types/job';
import { JobResponse } from '@/services/agentApi';


interface RecentTasksProps {
    jobs: any[],
    handleJobClick: (job: any) => void,
    handleReload: () => void,
    setIsJobIdDialogOpen: (open: boolean) => void,
    reloading: boolean,
    loading?: boolean,
}

function RecentTasks({
    jobs,
    handleJobClick,
    handleReload,
    reloading,
    setIsJobIdDialogOpen,
    loading = false,
}: RecentTasksProps) {

    const isEmergentUser = useIsEmergentUser();

    const [activeSection, setActiveSection] = useState<'recent' | 'deployed'>('recent');


    const getTaskTitle = (job: JobResponse) => {
      const isForked = job.payload.forked_from ? true : false;

      if (isForked) {
        // Find the original job from the jobs list
        const originalJob = jobs.find((j) => j.id === job.payload.forked_from);
        const originalTitle = originalJob
          ? originalJob.payload.original_task || originalJob.payload.task
          : job.payload.forked_from; // fallback to job ID if not found

        // Get all forks from the same original job and sort by created_at
        const allForks = jobs
          .filter((j) => j.payload.forked_from === job.payload.forked_from)
          .sort(
            (a, b) =>
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
          );

        // Find the position of this specific fork (1-based index)
        const forkPosition = allForks.findIndex((j) => j.id === job.id) + 1;

        return (
          <TableCell className="text-[#C2C2CC] text-[12px] md:text-[15px] px-6 w-[45%]">
            <span className="text-[#80FFF9] font-medium">
              Fork{" "}
              <span className="font-medium px-[6px] leading-[20px] rounded-[5px] bg-[#80FFF915]">{forkPosition}</span>{" "}
              from{" "}
            </span>
            - {originalTitle.slice(0, 100)}
          </TableCell>
        );
      } else {
        return (
          <TableCell className="text-[#C2C2CC] text-[12px] md:text-[15px] px-6 w-[45%]">
            {JSON.stringify(job.payload.task).slice(0, 100).replace(/"/g, "") ||
              "Write a test script for me to test out your testing..."}
          </TableCell>
        );
      }
    };

    const ShimmerRow = () => (
        <TableRow className="w-full hover:bg-transparent">
            <TableCell className="rounded-l-lg w-[15%]">
                <div className="h-4 w-16 bg-[#252629] rounded animate-pulse"></div>
            </TableCell>
            <TableCell className='w-[45%]'>
                <div className="h-4 w-full bg-[#252629] rounded animate-pulse "></div>
            </TableCell>
            <TableCell className="rounded-r-lg w-[20%]">
                <div className="h-4 bg-[#252629] rounded animate-pulse"></div>
            </TableCell>
        </TableRow>
    );

    return <>
        <div className="flex-col hidden mt-2 mb-8 space-y-5 md:flex">
            <div className="flex items-center justify-between">
                <div  className='flex items-center space-x-2 md:space-x-4'>
                    <div onClick={() => setActiveSection('recent')} className="flex items-center space-x-1 cursor-pointer">
                    <img alt='Recent tasks' src={RecentTask} className={cn("h-5 md:h-6 w-fit opacity-30", activeSection === 'recent' && 'opacity-100')} />
                    <h2 className={cn("text-[12px] md:text-lg text-white/30", activeSection === 'recent' && 'text-white')}>Recent Tasks</h2>
                </div>
                <div className='w-[2px] h-[18px] bg-white/20'></div>
                 <div onClick={() => setActiveSection('deployed')} className="flex items-center space-x-1 cursor-pointer">
                    <img alt='Recent tasks' src={activeSection === 'deployed' ? GlobalActive : GlobalWhite} className={cn("h-5 md:h-6  opacity-50 w-5 md:w-6", activeSection === 'deployed' && 'opacity-100')} />
                    <h2 className={cn("text-[12px] md:text-lg text-[#898E98]", activeSection === 'deployed' && 'bg-gradient-to-r from-[#80fff9] to-[#1588fc] text-transparent bg-clip-text')}>Deployed Apps</h2>
                </div>
                </div>
                <div className="flex gap-2">
                    {
                        isEmergentUser && (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="w-8 h-8 text-muted-foreground hover:bg-transparent"
                                onClick={() => setIsJobIdDialogOpen(true)}
                            >
                                <Plus className="w-4 h-4" />
                            </Button>
                        )
                    }

                    {
                        activeSection === 'recent' && (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="w-8 h-8 text-muted-foreground hover:bg-transparent"
                                onClick={handleReload}
                                disabled={reloading || loading}
                            >
                                <RotateCw
                                    className={`h-4 w-4 ${reloading ? "animate-spin" : ""}`}
                                />
                            </Button>
                        )
                    }
                </div>
            </div>
           {activeSection === 'recent' &&  <div className="relative rounded-2xl border border-[#252629] bg-[rgba(15,15,16,0.01)] backdrop-blur-[2.5px] overflow-clip">
                {/* Custom header */}
                <div className="sticky top-0 z-10">
                    <table className="w-full border-separate border-spacing-0">
                        <thead>
                            <tr className="bg-[#18181A]">
                                <th className="text-[#8F8F98] font-normal px-6 py-3 rounded-tl-lg text-sm font-berkeley w-[15%] text-left">
                                    ID
                                </th>
                                <th className="text-[#8F8F98] font-normal px-6 py-3 text-sm font-berkeley w-[45%] text-left">
                                    Task
                                </th>
                                {/* <th className="text-[#8F8F98] font-normal px-6 py-3 text-sm rounded-tr-lg font-berkeley w-[20%] text-left">
                                    Status
                                </th> */}
                            </tr>
                        </thead>
                    </table>
                </div>
                <div className="max-h-[500px] overflow-y-auto relative text-[12px]">
                    <Table className="relative w-full border-separate border-spacing-0">
                        {/* Sticky header */}
                        <TableHeader className="hidden">
                            <TableRow className="bg-[#18181A] hover:bg-[#18181A]">
                                <TableHead className="text-[#8F8F98] font-normal px-6 rounded-tl-lg text-sm font-berkeley w-[15%]">
                                    ID
                                </TableHead>
                                <TableHead className="text-[#8F8F98] font-normal px-6 text-sm font-berkeley w-[45%]">
                                    Task
                                </TableHead>
                                {/* <TableHead className="text-[#8F8F98] font-normal px-6 text-sm rounded-tr-lg font-berkeley w-[20%]">
                                    Status
                                </TableHead> */}
                            </TableRow>
                        </TableHeader>
                        <TableBody className='w-full'>
                            {loading ? (
                                // Show shimmer loading effect with 5 rows
                                Array(5).fill(0).map((_, index) => (
                                    <ShimmerRow key={`shimmer-${index}`} />
                                ))
                            ) : jobs.length == 0 ? (
                                <TableRow className="hover:bg-transparent">
                                    <TableCell colSpan={3} className="py-8 text-center hover:bg-transparent">
                                        <div className="flex flex-col items-center gap-4">
                                            <div className="text-[#8F8F98] text-sm">No tasks yet</div>
                                            <div className="text-[#92929A] text-xs max-w-lg">
                                                Click the plus button at the top to create your first task and start building
                                            </div>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                jobs.map((job) => (
                                    <TableRow
                                        key={job.id}
                                        className="hover:bg-[#1C1C1E] transition-colors cursor-pointer"
                                        onClick={() => handleJobClick(job)}
                                    >
                                        <TableCell className="text-[#92929A] text-[12px] md:text-[15px] px-6 py-4 w-[15%] rounded-l-lg">{`EMT - ${job.id.slice(0, 6)}`}</TableCell>
                                         {getTaskTitle(job)}
                                        {/* <TableCell className="text-[#C2C2CC] text-[12px] md:text-[16px] px-6 rounded-r-lg w-[20%]">
                                            {getStatusIndicator(job.status)}
                                        </TableCell> */}
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>}

            {activeSection === 'deployed' && <DeployedApps handleJobClick={handleJobClick}/>}
        </div>
    </>
}

export default RecentTasks