import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import AgentSleepingSVG from "@/assets/agentsleeping.svg";
import AgentBell from "@/assets/fluent-emoji_bell.svg";

// Component for displaying agent sleep notification

interface GitHubDetails {
  branch: string;
  repo: string;
  owner: string;
  provider: string;
}

interface AgentSleepNotificationProps {
  podIsPaused: boolean;
  isInputActive: boolean;
  showCase: boolean;
  githubDetails: GitHubDetails | null;
  createdBefore25April: boolean;
  resumePod: () => void;
  isWakingUp: boolean;
}

export const AgentSleepNotification: React.FC<AgentSleepNotificationProps> = ({
  podIsPaused,
  isInputActive,
  showCase,
  githubDetails,
  createdBefore25April,
  resumePod,
  isWakingUp,
}) => {
  return (
    <AnimatePresence>
      {podIsPaused && isInputActive && showCase !== true && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ type: "tween", ease: "easeOut", duration: 0.4 }}
          className={`absolute px-4 grayscale-0 bottom-0 left-0 right-0 z-[49] w-full`}
        >
          <div className="max-w-4xl mx-auto bg-[#1D1D1E] rounded-[1rem] rounded-bl-none rounded-br-none overflow-hidden p-2 pb-0 flex justify-between">
            <div className="w-full bg-[#131314] p-4 md:px-[2rem] rounded-[1rem] rounded-bl-none rounded-br-none md:pt-[2rem] md:pb-[3rem] flex md:flex-row flex-col justify-between md:items-center gap-3">
              <div className="flex items-center gap-5">
                <img
                  src={AgentSleepingSVG}
                  className="hidden w-8 h-8 md:block md:min-w-12 md:min-h-12"
                  alt="Agent Sleeping"
                />
                <div className="flex flex-col gap-1 font-inter">
                  <span className="text-sm md:text-[16px] font-medium text-white">
                    Agent went to sleep due to inactivity
                  </span>
                  {/* Show different messages based on GitHub status and creation date */}
                  {githubDetails && createdBefore25April ? (
                    <span className="text-[#7b7b80] font-['Inter'] text-sm">
                      Apologies, the task has expired due to inactivity.
                      We may not be able to recover this at this moment.
                      Please start a new task using the same github repo
                      or reach out to us at{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text  font-semibold underline underline-offset-1"
                      >
                        <EMAIL>
                      </a>{" "}
                      for any assistance.
                    </span>
                  ) : githubDetails ? (
                    <span className="text-[#7b7b80] font-['Inter'] text-sm">
                      Your GitHub task is paused. Click 'Wake up the
                      Agent' to continue. Need help? Email{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text  font-semibold underline underline-offset-1"
                      >
                        <EMAIL>
                      </a>
                    </span>
                  ) : (
                    <span className="text-[#7b7b80] font-['Inter'] text-sm">
                      If you are having trouble accessing your work,
                      Please contact support at{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text  font-semibold underline underline-offset-1"
                      >
                        <EMAIL>
                      </a>
                    </span>
                  )}
                </div>
              </div>

              {/* Hide wakeup button if created before April 25, 2024 AND created using GitHub */}
              {githubDetails && createdBefore25April ? null : (
                <button
                  type="button"
                  onClick={resumePod}
                  disabled={isWakingUp}
                  className="bg-[#FCB94920] max-w-[200px] disabled:opacity-25 p-[10px] rounded-[6px] flex items-center gap-2 md:min-w-[200px] hover:bg-[#FCB94930] transition-colors duration-200"
                >
                  <img
                    src={AgentBell}
                    alt="Wake icon"
                    className="w-5 h-5"
                  />
                  <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent text-[12px] md:text-[16px]  bg-clip-text font-medium">
                    Wake up the Agent
                  </span>
                </button>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
