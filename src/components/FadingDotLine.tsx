import React from 'react';

// Define color interface
interface RGBColor {
  r: number;
  g: number;
  b: number;
}

// Define dot interface
interface Dot {
  index: number;
  opacity: number;
  color: string;
}

const FadingDotLine: React.FC = () => {
  // Define colors
  const darkColor: RGBColor = { r: 26, g: 39, b: 39 };  // #1A2727
  const brightColor: RGBColor = { r: 128, g: 255, b: 249 }; // #80FFF9

  // Create dots for left and right sides - responsive based on screen size
  const dotsPerSide: number = window.innerWidth < 640 ? 6 : 10;

  // Generate dots with varying opacity and color
  const generateDots = (side: 'left' | 'right'): Dot[] => {
    return Array.from({ length: dotsPerSide }, (_, index) => {
      // Calculate distance ratio (0 to 1)
      const distanceRatio: number = index / (dotsPerSide - 1);

      // Calculate opacity based on distance from the edge
      const opacity: number = side === 'left'
        ? Math.max(0.2, distanceRatio)
        : Math.max(0.2, 1 - distanceRatio);

      // Calculate color based on position
      let color: string;
      if (side === 'left') {
        // Left side: darkColor to brightColor
        const r: number = Math.round(darkColor.r + (brightColor.r - darkColor.r) * distanceRatio);
        const g: number = Math.round(darkColor.g + (brightColor.g - darkColor.g) * distanceRatio);
        const b: number = Math.round(darkColor.b + (brightColor.b - darkColor.b) * distanceRatio);
        color = `rgb(${r}, ${g}, ${b})`;
      } else {
        // Right side: brightColor to darkColor
        const r: number = Math.round(brightColor.r + (darkColor.r - brightColor.r) * distanceRatio);
        const g: number = Math.round(brightColor.g + (darkColor.g - brightColor.g) * distanceRatio);
        const b: number = Math.round(brightColor.b + (darkColor.b - brightColor.b) * distanceRatio);
        color = `rgb(${r}, ${g}, ${b})`;
      }

      return { index, opacity, color };
    });
  };

  const leftDots: Dot[] = generateDots('left');
  const rightDots: Dot[] = generateDots('right');

  return (
    <div className="flex flex-col items-center justify-center w-full p-4 pt-8 pb-0 rounded-lg md:p-8 hidden md:flex">
      <div className="flex items-center justify-center w-full">
        {/* Left dots - #1A2727 to #80FFF9 */}
        {leftDots.map((dot) => (
          <div
            key={`left-${dot.index}`}
            className="mx-1 rounded-full md:mx-2"
            style={{
              width: window.innerWidth < 640 ? '3px' : '4px',
              height: window.innerWidth < 640 ? '3px' : '4px',
              opacity: dot.opacity,
              backgroundColor: dot.color
            }}
          />
        ))}

        {/* Center text */}
        <div className="mx-2 text-nowrap  md:mx-4 md:text-xl animate-text bg-gradient-to-r from-[#80FFF9] to-[#F7E7D9] text-transparent bg-clip-text">
          From the Community
        </div>

        {/* Right dots - #80FFF9 to #1A2727 */}
        {rightDots.map((dot) => (
          <div
            key={`right-${dot.index}`}
            className="mx-1 rounded-full md:mx-2"
            style={{
              width: window.innerWidth < 640 ? '3px' : '4px',
              height: window.innerWidth < 640 ? '3px' : '4px',
              opacity: dot.opacity,
              backgroundColor: dot.color
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default FadingDotLine;
