import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useAuth, useCredits } from "@/contexts";
import {  buttonVariants } from "@/components/ui/button";
import DottedLayout from "./DottedBackgroundLayout";
import TabBar, { TabContents, useTabState } from '../TabBar';
import { CreditsDisplay } from "../CreditsDisplay";
import { useGitHub } from "@/hooks/useGitHubAPI";
import { Link } from "react-router";
import AlertBanner from "@/components/ui/alert-banner";
import { useAlert } from "@/contexts/AlertContext";
import { useBannerHeight } from "@/hooks/useBannerHeight";
import BuyCreditIcon from "@/assets/buy_credit.svg"
import { motion, AnimatePresence } from "framer-motion";

import MenuBar from "../MenuBar";
import { githubConfig } from "@/config";
import { useConfig } from "@/hooks/useConfig";
import { UpgradeTierModal } from "../modals/UpgradeTierModal";
import useIsMobileApp from "@/hooks/useIsMobileApp";

interface ProtectedLayoutProps {
  children?: React.ReactNode;
  hideNav?: boolean;
  className?: string;
}

export default function ProtectedLayout({
  hideNav = false,
  className,
}: ProtectedLayoutProps) {

  // States
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut ] = useState(false);
  const [showCredits, setShowCredits] = useState(true);

  const buttonStyle = {
    background: `
      linear-gradient(90deg, #ffffff30, #00000050),
      #F3CA5F
    `,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundBlendMode: 'overlay, normal',
  };

  // Hooks Call
  const { user, signOut, session } = useAuth();
  const { setTabs, setActiveTab } = useTabState();
  const {config} = useConfig();
  const { isConnected: isGitHubConnected, checkGitHubConnection, setIsConnected } = useGitHub();
  const { tier } = useCredits();
  const isMobileApp = useIsMobileApp();

  useEffect(() => {
    if (isMobileApp) {
      setShowCredits(false);
    }
  }, [isMobileApp]);

  // Banner management
  const { bannerConfig, hideBanner, showWarning } = useAlert();
  const bannerRef = useBannerHeight();

  useEffect(() => {
    if (config?.info_banner && config.info_banner.show) {
      showWarning(config.info_banner.content, config.info_banner.dismissible);
    }
  }, [config, showWarning]);

  // Functions
  const handleSignOut =async() => {
    setIsLoggingOut(true);
    setTabs(tabs => tabs.filter(tab => tab.id === "home"));

    setIsConnected(false);
    setActiveTab("home");

    await signOut().then(()=>{
      setIsLoggingOut(false);
    });
  }

  const handleBannerDismiss = () => {
    hideBanner();
  };
  
  const handleGitHubConfigClick = () => {
    const githubAppUrl = githubConfig.appUrl;
      window.open(githubAppUrl, '_blank');
  };

  useEffect(() => {
    if (session) {
      checkGitHubConnection();
    }
  }, [session]);

  const { getActiveTab } = useTabState();
  const activeTab = getActiveTab();
  const isOnChatScreen = activeTab?.id !== "home";




  return (
    <DottedLayout>
      <div className={cn("flex h-full flex-col", className)}>
        {!hideNav && (
          <header className="sticky top-0 z-50  bg-[#0f0f0f] md:bg-[rgba(30,30,31,0.8)]">
            <AnimatePresence>
              {bannerConfig.show && (
                <motion.div
                  ref={bannerRef}
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{
                    duration: 0.3,
                    ease: [0.25, 0.1, 0.25, 1]
                  }}
                  style={{ overflow: "hidden" }}
                >
                  <AlertBanner
                    level={bannerConfig.level}
                    content={bannerConfig.content}
                    iconUrl={bannerConfig.iconUrl}
                    dismissible={bannerConfig.dismissible}
                    onDismiss={handleBannerDismiss}
                  />
                </motion.div>
              )}
            </AnimatePresence>
            <div className="flex items-center px-2 h-[56px] max-h-[56px] md:px-6 container-fluid">
              <div className="flex-1 overflow-hidden mr-4 w-full md:max-w-[calc(100%-200px)]">
                <TabBar />
              </div>

              {!session && (
                <div className="flex items-center gap-[10px] ">
                  <Link
                    to="/login"
                    className={cn(
                      buttonVariants({ variant: "outline" }),
                      "text-white border-[1px] border-[#3D3D3D] px-[20px] py-[8px] h-[36px]"
                    )}
                  >
                    {" "}
                    Log in
                  </Link>
                  <Link
                    to="/register"
                    className={cn(
                      buttonVariants({ variant: "default" }),
                      "text-[#1A1A1A]  bg-white  px-[20px] py-[8px] h-[36px]"
                    )}
                  >
                    {" "}
                    Sign up
                  </Link>
                </div>
              )}

              {session && (
                <div className="flex items-center gap-2 justify-end flex-shrink-0 md:min-w-[180px]">
                  <CreditsDisplay />
                  {tier === "free" && showCredits && (
                    <button
                      onClick={() => setIsModalOpen(true)}
                      title="Buy Credits"
                      style={buttonStyle}
                      className={`${isOnChatScreen ? 'pl-[8px] pr-[8px]' : 'pl-[8px] pr-[12px] gap-[4px]'} relative flex items-center  py-[6px] rounded-full font-[600] text-[14px] text-[#0F0F10] ${isOnChatScreen ? 'mr-10' : 'mr-0'}`}
                    >
                      <img
                        src={BuyCreditIcon}
                        alt="right arrow"
                        className="w-5 h-5"
                      />
                      { !isOnChatScreen && <span className="tracking-[-0.2px]">Buy Credits</span>}
                    </button>
                  )}

                  <div className="items-center justify-end hidden gap-4 md:flex">
                    <MenuBar
                      isLoggingOut={isLoggingOut}
                      user={user}
                      handleGitHubConfigClick={handleGitHubConfigClick}
                      handleLogout={handleSignOut}
                      isGitHubConnected={isGitHubConnected}
                    />
                  </div>
                </div>
              )}
            </div>
          </header>
        )}
      
        <TabContents />
        
        <UpgradeTierModal isOpen={isModalOpen} onOpenChange={setIsModalOpen} />
      </div>
    </DottedLayout>
  );
}