import { X, Download } from "lucide-react";
import { useEffect } from "react";
import { createPortal } from "react-dom";
import { downloadFileFromUrl } from "@/lib/utils/downloadFile";

interface ImagePreviewUrlProps {
  imageUrl: string | null;
  imageName?: string;
  isOpen: boolean;
  onClose: () => void;
}

/**
 * A full-screen image preview component for URL-based images
 * Used for asset cards and other URL-based image previews
 */
export function ImagePreviewUrl({ imageUrl, imageName, isOpen, onClose }: ImagePreviewUrlProps) {
  // Handle escape key to close preview
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when preview is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleDownload = async () => {
    if (!imageUrl) return;

    try {
      // Set the download filename
      const fileName = imageName || `image-${new Date().getTime()}`;
      // Extract extension from URL
      const extension = imageUrl.split('.').pop()?.split('?')[0] || 'png';
      const finalFileName = fileName.includes('.') ? fileName : `${fileName}.${extension}`;

      await downloadFileFromUrl(imageUrl, finalFileName);
    } catch (error) {
      console.error('Error downloading image:', error);
      // Fallback: open in new tab
      window.open(imageUrl, '_blank');
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Only close if clicking on the backdrop, not the image
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen || !imageUrl) return null;

  return createPortal(
    <div
      className="fixed inset-0 z-[999] flex items-center justify-center bg-black/80 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      {/* Header with controls */}
      <div className="absolute top-0 left-0 right-0 z-10 flex items-center justify-between p-4 bg-gradient-to-b from-black/50 to-transparent">
        <div className="text-sm truncate text-white/70 max-w-[80%]">
          {imageName ? `${imageName} - Image Preview` : 'Image Preview'}
        </div>
        <div className="flex items-center gap-2">
          <button
            type="button"
            onClick={handleDownload}
            className="p-2 transition-all duration-200 rounded-lg text-white/70 hover:text-white bg-white/10 hover:bg-white/20"
            title="Download image"
          >
            <Download className="w-5 h-5" />
          </button>
          <button
            type="button"
            onClick={onClose}
            className="p-2 transition-all duration-200 rounded-lg text-white/70 hover:text-white bg-white/10 hover:bg-white/20"
            title="Close preview"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Image container */}
      <div className="relative flex items-center justify-center w-full h-full p-8 pt-20">
        <img
          src={imageUrl}
          alt={imageName || "Full size preview"}
          className="object-contain max-w-full max-h-full rounded-lg shadow-2xl min-h-[300px] min-w-[300px]"
          onClick={(e) => e.stopPropagation()} // Prevent closing when clicking on image
        />
      </div>
    </div>,
    document.body
  );
}
