import { cn } from "@/lib/utils";
import { ResponseImageData } from "@/types/message";
import { useState } from "react";
import { ImagePreview } from "./ImagePreview";

interface ImageGalleryProps {
  images: ResponseImageData[];
  className?: string;
}

/**
 * A reusable component for displaying a gallery of images from base64 data
 * Used in both message items and subagent message panels
 */
export function ImageGallery({ images, className }: ImageGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<ResponseImageData | null>(null);

  if (!images || images.length === 0) return null;

  return (
    <>
      <div
        className={cn(
          "grid gap-2 z-[49] relative my-4 rounded-lg w-full",
          images.length === 1 ? "grid-cols-1" :
            images.length === 2 ? "grid-cols-2" :
              "grid-cols-2 md:grid-cols-3",
          className
        )}
      >
        {images.map((image, index) => (
          <div
            key={index}
            className="relative rounded-lg max-h-[300px] overflow-hidden bg-white/10 flex items-center justify-center cursor-pointer"
            onClick={() => setSelectedImage(image)}
          >
            <img
              src={`data:${image.mime_type};base64,${image.img_base64}`}
              alt={`Image ${index + 1}`}
              className="w-full h-full object-contain rounded-lg max-h-[300px]"
            />
          </div>
        ))}
      </div>

      <ImagePreview
        image={selectedImage}
        isOpen={!!selectedImage}
        onClose={() => setSelectedImage(null)}
      />
    </>
  );
}
