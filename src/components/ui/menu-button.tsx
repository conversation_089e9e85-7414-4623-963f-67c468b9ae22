import { cn } from "@/lib/utils";
import { useState } from "react";
import { motion } from "framer-motion";

interface MenuButtonProps {
  hoverIcon: string;
  icon: string;
  className?: string;
  children?: React.ReactNode;
  onClick?: () => void;
  hoveringEndIcon?: string;
  buttonClassName?: string;
  disabled?: boolean;
  loadingState?: boolean;
  loadingNode?: React.ReactNode;
}

function MenuButton({
  hoverIcon,
  icon,
  className,
  children,
  onClick,
  hoveringEndIcon,
  buttonClassName,
  loadingState = false,
  loadingNode,
  disabled = false,
}: MenuButtonProps) {
  const [isHovering, setIsHovering] = useState(false);

  return (
    <motion.button
      disabled={disabled}
      onClick={onClick}
      type="button"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      className={cn("flex font-medium px-0 py-[10px] gap-3 justify-between items-center", className)}
      // Removed the whileHover as it was conflicting with Tailwind hover classes
      transition={{ duration: 0.15 }}

    >
      <div className={cn("flex items-center gap-3" )}>
        {icon && !loadingState && (
          <motion.img
            src={isHovering && hoverIcon ? hoverIcon : icon}
            alt="icon"
            className="w-6 h-6 transition-all ease-in-out group-hover/rotate:-rotate-12"
            initial={{ opacity: 1 }}
            animate={{ 
              // opacity: isHovering ? 1 : className?.includes("opacity") ? 1 : 1,
            //   scale: isHovering ? 1.05 : 1 
            }}
            transition={{ duration: 0.2 }}
          />
        )}

        {loadingState && loadingNode}

        <motion.span
          animate={{ 
            // opacity: isHovering ? 1 : className?.includes("opacity") ? 1 : 1
          }}
          transition={{ duration: 0.2 }}
        >
          {children}
        </motion.span>
      </div>
      
      {hoveringEndIcon && (
        <motion.img
          src={hoveringEndIcon}
          alt="end icon"
          className="w-5 h-5"
          initial={{ opacity: 0, x: -5 }}
          animate={{ 
            opacity: isHovering ? 1 : 0,
            x: isHovering ? 0 : -5
          }}
          transition={{ duration: 0.2 }}
        />
      )}
    </motion.button>
  );
}

export default MenuButton;