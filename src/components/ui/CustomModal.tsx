import React, { ReactNode } from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

export interface CustomModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description?: string;
  children: ReactNode;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
  footer?: ReactNode;
  headerActions?: ReactNode;
  className?: string;
  contentClassName?: string;
  backdropClassName?: string;
  maxHeight?: string;
  minHeight?: string;
  width?: string;
  minWidth?: string;
  maxWidth?: string;
  padding?: string;
  borderRadius?: string;
  backgroundColor?: string;
  borderColor?: string;
  backdropBlur?: boolean;
  zIndex?: number;
  showBorder?: boolean;
  customHeader?: ReactNode;
  hideHeader?: boolean;
  scrollable?: boolean;
  centered?: boolean;
  animation?: "fade" | "slide" | "scale" | "none";
}

const sizeClasses = {
  sm: "max-w-sm",
  md: "max-w-md", 
  lg: "max-w-lg",
  xl: "max-w-xl",
  full: "max-w-full"
};

const animationVariants = {
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  slide: {
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 50 }
  },
  scale: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 }
  },
  none: {
    initial: {},
    animate: {},
    exit: {}
  }
};

export const CustomModal: React.FC<CustomModalProps> = ({
  isOpen,
  onOpenChange,
  title,
  description,
  children,
  size = "md",
  showCloseButton = true,
  closeOnBackdropClick = false,
  footer,
  headerActions,
  className,
  contentClassName,
  backdropClassName,
  maxHeight = "calc(100vh - 56px)",
  minHeight,
  width,
  minWidth,
  maxWidth,
  padding = "p-0",
  borderRadius = "rounded-[16px]",
  backgroundColor = "bg-[#18181A]",
  borderColor = "border-[#242424]",
  backdropBlur = true,
  zIndex = 49,
  showBorder = true,
  customHeader,
  hideHeader = false,
  scrollable = true,
  centered = true,
  animation = "fade"
}) => {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (closeOnBackdropClick && e.target === e.currentTarget) {
      onOpenChange(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  if (!isOpen) return null;

  const modalVariants = animationVariants[animation];

  return (
    <div 
      className={cn(
        "absolute w-full flex items-center justify-center",
        backdropBlur && "backdrop-blur-[5px]",
        centered ? "min-h-[calc(100vh-56px)]" : "",
        `z-[${zIndex}]`,
        backdropClassName
      )}
      style={{ 
        maxHeight,
        minHeight: minHeight || (centered ? maxHeight : undefined),
        background: "rgba(14, 14, 15, 0.5)"
      }}
      onClick={handleBackdropClick}
    >
      <AnimatePresence>
        {isOpen && (
          <motion.div
            {...modalVariants}
            transition={{ duration: 0.2, ease: "easeInOut" }}
            className={cn(
              padding,
              "max-md:max-w-[95vw]",
              backgroundColor,
              showBorder && `border ${borderColor}`,
              borderRadius,
              "font-['Inter'] overflow-hidden",
              sizeClasses[size],
              width && `w-[${width}]`,
              minWidth && `min-w-[${minWidth}]`,
              maxWidth && `max-w-[${maxWidth}]`,
              className
            )}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            {!hideHeader && (
              <div className={cn(
                "flex justify-between items-center",
                customHeader ? "" : "p-4 md:p-6"
              )}>
                {customHeader ? (
                  customHeader
                ) : (
                  <>
                    <div className="flex flex-col gap-2">
                      {title && (
                        <h2 className="text-[18px] md:text-[22px] font-medium text-white">
                          {title}
                        </h2>
                      )}
                      {description && (
                        <p className="text-[13px] md:text-[16px] font-[400] md:leading-[24px] text-[#8A8F98] font-['Inter']">
                          {description}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {headerActions}
                      {showCloseButton && (
                        <button
                          type="button"
                          onClick={handleClose}
                          className="w-6 h-6 flex items-center justify-center text-[#737780] hover:text-white transition-colors"
                          aria-label="Close modal"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      )}
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Content */}
            <div className={cn(
              "flex flex-col",
              scrollable && "overflow-y-auto",
              contentClassName
            )}>
              {children}
            </div>

            {/* Footer */}
            {footer && (
              <div className="border-t border-[#242424] p-3 md:p-6">
                {footer}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Preset configurations for common modal types
export const ModalPresets = {
  confirmation: {
    size: "sm" as const,
    showCloseButton: true,
    closeOnBackdropClick: false,
    animation: "scale" as const
  },
  form: {
    size: "md" as const,
    showCloseButton: true,
    closeOnBackdropClick: false,
    scrollable: true,
    animation: "slide" as const
  },
  fullscreen: {
    size: "full" as const,
    maxHeight: "100vh",
    minHeight: "100vh",
    borderRadius: "rounded-none",
    animation: "fade" as const
  },
  sidebar: {
    size: "lg" as const,
    centered: false,
    animation: "slide" as const,
    maxHeight: "100vh",
    minHeight: "100vh"
  }
};

export default CustomModal;
