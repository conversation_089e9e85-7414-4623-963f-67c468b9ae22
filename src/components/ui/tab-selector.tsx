import React from 'react';
import { cn } from '@/lib/utils';

export interface TabSelectorProps {
  tabs: { id: string; label: string, icon: React.ReactNode }[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
  fontSize?: string;
  activeTabBgColor?: string;
  fontColor?: string;
  padding?: string;
}

export interface TabSelectorContentProps {
  tabId: string;
  activeTab: string;
  children: React.ReactNode;
  className?: string;
}

export function TabSelector({
  tabs,
  activeTab,
  onTabChange,
  className,
  fontSize = 'text-base',
  activeTabBgColor = 'bg-[#272729]',
  fontColor = 'text-white',
  padding = 'py-2 md:py-3'
}: TabSelectorProps) {
  return (
    <div className={cn('w-full', className)}>
      <div className="grid w-full grid-cols-2 rounded-xl">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              `rounded-lg ${padding} ${fontColor} font-[Brockmann] ${fontSize} flex justify-center items-center gap-2 font-medium transition-colors`,
              activeTab === tab.id ? activeTabBgColor : 'opacity-30'
            )}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>
    </div>
  );
}

export function TabSelectorContent({
  tabId,
  activeTab,
  children,
  className
}: TabSelectorContentProps) {
  if (tabId !== activeTab) return null;
  
  return (
    <div className={cn('mt-4', className)}>
      {children}
    </div>
  );
}
