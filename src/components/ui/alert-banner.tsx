import React from 'react';
import { cn } from '@/lib/utils';
import { X } from 'lucide-react';

import AlertError from "@/assets/alert-fill.svg";
import AlertWarning from "@/assets/alert-orange.svg";

export type AlertLevel = 'info' | 'warning' | 'error' | 'success';

interface AlertBannerProps {
  level: AlertLevel;
  content: string;
  iconUrl?: string;
  onDismiss?: () => void;
  dismissible?: boolean;
  className?: string;
}

const alertStyles: Record<AlertLevel, string> = {
  info: 'bg-blue-600 text-white',
  warning: 'bg-[#E38F4529] text-[#E38F45]',
  error: 'bg-[#FF808029] text-[#FF8080]',
  success: 'bg-green-600 text-white',
};

const AlertBanner: React.FC<AlertBannerProps> = ({
  level,
  content,
  iconUrl,
  onDismiss,
  dismissible = false,
  className,
}) => {

  return (
    <div
      id={"alert-banner"}
      className={cn(
        "flex items-center justify-center px-4 py-3 text-sm font-medium",
        alertStyles[level],
        className
      )}
    >
      <div className="flex items-center justify-center w-full gap-1 mx-auto md:gap-[10px] max-w-7xl">
        {level != "info" && (
          <img
            src={level === "error" ? AlertError : AlertWarning}
            alt="Alert"
            className="flex-shrink-0 w-5 h-5"
          />
        )}

        <div className="font-medium text-center w-fit tracking-[-0.2px]">
          {content}
        </div>

        {dismissible && onDismiss && (
          <button
            onClick={onDismiss}
            className="absolute flex-shrink-0 p-1 transition-colors rounded right-1 md:right-4 hover:bg-black/10"
            aria-label="Dismiss alert"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default AlertBanner;
