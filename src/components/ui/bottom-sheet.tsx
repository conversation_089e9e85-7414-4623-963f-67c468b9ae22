import * as React from "react"
import { Button } from "@/components/ui/button"
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  Drawer<PERSON>eader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";

interface BottomSheetProps {
  trigger: React.ReactNode
  title?: string
  description?: string
  children: React.ReactNode
  footer?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  maxWidth?: string
  showDefaultFooter?: boolean
  onSubmit?: () => void
  onCancel?: () => void
  submitText?: string
  cancelText?: string
  submitDisabled?: boolean
}

export function BottomSheet({
  trigger,
  title,
  description,
  children,
  footer,
  open,
  onOpenChange,
  maxWidth = "max-w-sm",
  showDefaultFooter = true,
  onSubmit,
  onCancel,
  submitText = "Submit",
  cancelText = "Cancel",
  submitDisabled = false,
}: BottomSheetProps) {
  const handleSubmit = () => {
    onSubmit?.()
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>
        {trigger}
      </DrawerTrigger>
      <DrawerContent>
        <div className={`mx-auto w-full ${maxWidth}`}>
          {(title || description) && (
            <DrawerHeader>
              {title && <DrawerTitle>{title}</DrawerTitle>}
              {description && <DrawerDescription>{description}</DrawerDescription>}
            </DrawerHeader>
          )}
          
          <div className="p-3 pb-0">
            {children}
          </div>

          {(footer || showDefaultFooter) && (
            <DrawerFooter>
              {footer || (
                <>
                  <Button onClick={handleSubmit} disabled={submitDisabled}>
                    {submitText}
                  </Button>
                  <DrawerClose asChild>
                    <Button variant="outline" onClick={handleCancel}>
                      {cancelText}
                    </Button>
                  </DrawerClose>
                </>
              )}
            </DrawerFooter>
          )}
        </div>
      </DrawerContent>
    </Drawer>
  )
}
