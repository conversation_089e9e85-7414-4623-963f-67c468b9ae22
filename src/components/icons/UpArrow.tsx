interface UpArrowProps {
    color?: string;
    size?: number;
    className?: string;
    direction?: 'up' | 'down' | 'left' | 'right';
  }
  
  const UpArrow: React.FC<UpArrowProps> = ({ 
    color = "#0F0F10", 
    size = 24, 
    className = "",
    direction = 'up'
  }) => {
    const getRotation = () => {
      switch (direction) {
        case 'up': return 'rotate(0deg)';
        case 'down': return 'rotate(180deg)';
        case 'left': return 'rotate(-90deg)';
        case 'right': return 'rotate(90deg)';
        default: return 'rotate(0deg)';
      }
    };
  
    return (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none"
        className={className}
        style={{ transform: getRotation() }}
      >
        <path 
          fillRule="evenodd" 
          clipRule="evenodd" 
          d="M11.1294 4.36049C11.61 3.87984 12.3894 3.87984 12.87 4.36049L18.4084 9.89892C18.889 10.3796 18.889 11.1588 18.4084 11.6394C17.9277 12.1202 17.1485 12.1202 16.6679 11.6394L13.2304 8.20213L13.2304 18.7692C13.2304 19.4489 12.6794 20 11.9996 20C11.32 20 10.7689 19.4489 10.7689 18.7692L10.7689 8.20213L7.3315 11.6394C6.85087 12.1202 6.07161 12.1202 5.59098 11.6394C5.1103 11.1588 5.1103 10.3796 5.59098 9.89892L11.1294 4.36049Z" 
          fill={color}
        />
      </svg>
    );
  };

  export default UpArrow;