interface LinkIconProps {
    color?: string;
    size?: number;
    className?: string;
  }
  
  const LinkIcon: React.FC<LinkIconProps> = ({ 
    color = "#737380", 
    size = 20, 
    className = "" 
  }) => {
    return (
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 20 20" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <path 
          d="M6.43188 5.13649C6.43193 4.83302 6.5525 4.54199 6.76709 4.3274C6.98168 4.11281 7.27271 3.99223 7.57619 3.99219L14.8596 3.99219C15.1631 3.99223 15.4541 4.11281 15.6687 4.3274C15.8833 4.54199 16.0039 4.83302 16.0039 5.13649V12.4199C15.9987 12.72 15.8758 13.0059 15.6618 13.2163C15.4478 13.4266 15.1597 13.5445 14.8596 13.5445C14.5595 13.5445 14.2714 13.4266 14.0574 13.2163C13.8434 13.0059 13.7205 12.72 13.7153 12.4199L13.6457 7.96893L5.95765 15.657C5.74302 15.8716 5.45192 15.9922 5.14838 15.9922C4.84485 15.9922 4.55375 15.8716 4.33912 15.657C4.12448 15.4423 4.00391 15.1512 4.00391 14.8477C4.00391 14.5442 4.12448 14.2531 4.33912 14.0384L12.0272 6.35039L7.57619 6.2808C7.27271 6.28075 6.98168 6.16018 6.76709 5.94559C6.5525 5.731 6.43193 5.43997 6.43188 5.13649Z" 
          fill={color}
        />
      </svg>
    );
  };

  export default LinkIcon;