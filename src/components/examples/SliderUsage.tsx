import { Slider, SliderCard } from "@/components/ui/Slider";

import FirstCard from "@/assets/showcase/1.png"
import SecondCard from "@/assets/showcase/2.png"
import ThirdCard from "@/assets/showcase/3.png"
import FourthCard from "@/assets/showcase/4.png"
import FifthCard from "@/assets/showcase/6.png"

 export const imageCards: SliderCard[] = [
    {
      id: "image1",
      content: (
        <div className="flex items-center justify-center w-full h-full p-2 md:p-[10px] bg-gradient-to-b from-[#FFFFFF90] to-[#FFFFFF66] backdrop-blur-[112.05px] rounded-[20px] login-slider-card">
          <img src={FirstCard} alt="Image 1" className="object-cover h-full rounded-[16px]" />
        </div>
      ),
    },
    {
      id: "image2",
       content: (
        <div className="flex items-center justify-center w-full h-full p-2 md:p-[10px] bg-gradient-to-b from-[#FFFFFF90] to-[#FFFFFF66] backdrop-blur-[112.05px] rounded-[20px] login-slider-card">
          
          <img src={SecondCard} alt="Image 1" className="object-cover h-full rounded-[16px]" />
        </div>
      ),
    },
    {
      id: "image3",
      content: (
        <div className="flex items-center justify-center w-full h-full p-2 md:p-[10px] bg-gradient-to-b from-[#FFFFFF90] to-[#FFFFFF66] backdrop-blur-[112.05px] rounded-[20px] login-slider-card">
          
          <img src={ThirdCard} alt="Image 1" className="object-cover h-full rounded-[16px]" />
        </div>
      ),
    },
     {
      id: "image4",
        content: (
        <div className="flex items-center justify-center w-full h-full p-2 md:p-[10px] bg-gradient-to-b from-[#FFFFFF90] to-[#FFFFFF66] backdrop-blur-[112.05px] rounded-[20px] login-slider-card">
          
          <img src={FourthCard} alt="Image 1" className="object-cover w-full h-full rounded-[16px]" />
        </div>
      ),
    },
     {
      id: "image5",
       content: (
        <div className="flex items-center justify-center w-full h-full p-2 md:p-[10px] bg-gradient-to-b from-[#FFFFFF90] to-[#FFFFFF66] backdrop-blur-[112.05px] rounded-[20px] login-slider-card">
          
          <img src={FifthCard} alt="Image 1" className="object-cover h-full w-full rounded-[16px]" />
        </div>
      ),
    },
  ];
  
const SliderUsage = () => {

  const handleCardClick = (card: SliderCard, index: number) => {
    console.log("Card clicked:", card.id, "at index:", index);
  };

  return (
        <section className="flex flex-col items-center justify-center h-screen">
          <h2 className="mb-6 z-[10] text-[32px]  leading-[32px] tracking-[-0.03em] font-medium text-center text-white">
            Build Ambitious App <br/>
            With AI
          </h2>
          <Slider
            cards={imageCards}
            onCardClick={handleCardClick}
            autoAdvance={true}
            autoAdvanceDuration={2000}
            centerCardWidth={624}
            centerCardHeight={356}
            sideCardWidth={420}
            sideCardHeight={300}
            spacing={520}
            sideCardOpacity={0.7}
            sideCardScale={0.9}
            className="mb-8"
          />
        </section>
  );
};

export default SliderUsage;
