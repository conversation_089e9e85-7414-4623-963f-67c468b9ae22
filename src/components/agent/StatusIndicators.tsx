import AlertFillSVG from "@/assets/alert-fill.svg";
import AlertOrangeIcon from "@/assets/alert-orange.svg";
import { TokenDisplay } from "../TokenDisplay";
import ForkIcon from "@/assets/fork/fork.svg";
import Question from "@/assets/payments/question.svg"
import { URL_LINKS } from "@/constants/constants";
import {
  Tooltip,
  TooltipArrow,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { cn } from "@/lib/utils";

interface StatusIndicatorsProps {
  message: {
    action?: string;
    acc_cost?: number;
    max_budget?: number;
    current_token_count?: number;
    max_token_count?: number;
    error_message_exists?: boolean;
  };
  credits?: number;
  handleAddToken?: (variant: "increase_budget" | "add_credits") => void;
  podIsPaused?: boolean;
  modalOpen?: {
    fork: boolean;
  };
  setModalOpen?: (open: {
    fork: boolean;
  }) => void;
}

export const StatusIndicators = ({
  message,
  credits,
  handleAddToken,
  podIsPaused,
  modalOpen,
  setModalOpen,
}: StatusIndicatorsProps) => {


  const getTokenUsagePercentage = (
    currentTokenCount: number | undefined,
    maxTokenCount: number | undefined,
    errorMessageExists: any
  ): number => {
    if (!currentTokenCount || !maxTokenCount || maxTokenCount === 0) {
      return 0;
    }
    return Math.min(
      errorMessageExists ? 100 : 99,
      Math.round((currentTokenCount / maxTokenCount) * 100)
    );
  };

  const getTokenUsageColor = (errorMessageExists: any): string => {
    return !errorMessageExists ? "text-[#F2994A]" : "text-[#E55C5C]";
  };

  const formatTokenCount = (
    tokenCount: number | undefined,
    maxTokenCount?: number | undefined
  ): number => {
    if (!tokenCount) {
      return 0;
    }
    const safeTokenCount = maxTokenCount
      ? Math.min(tokenCount, maxTokenCount)
      : tokenCount;
    return Math.round(safeTokenCount / 1000);
  };

  // Credit exhausted
  if (message.action === "exit_cost_credit_limit_reached") {
    return (
      <div className="flex items-center justify-between gap-2 p-3 bg-[#1b1b1b] rounded-lg text-sm">
        <div className="flex items-center justify-center gap-2">
          <img
            src={AlertFillSVG}
            alt="Alert"
            className="w-5 h-5 text-[#E55C5C]"
          />
          <p className="text-[#E6E6E6] font-['Inter'] leading-6 font-normal">
            Add More Credits
          </p>
        </div>
      </div>
    );
  }

  // Budget exhausted
  if (message.action === "exit_cost") {
    return (
      <div className="flex items-center justify-between gap-2 p-3 bg-[#1b1b1b] rounded-lg text-sm">
        <div className="flex items-center justify-center gap-2">
          <img
            src={AlertFillSVG}
            alt="Alert"
            className="w-5 h-5 text-[#E55C5C]"
          />
          <p className="text-[#E6E6E6] font-['Inter'] leading-6 font-normal">
            Budget Exhausted
          </p>
        </div>
        <TokenDisplay
          acc_cost={message.acc_cost}
          max_budget={message.max_budget}
          onAddToken={() => {
            handleAddToken && !podIsPaused && handleAddToken("increase_budget");
          }}
        />
      </div>
    );
  }

  // Context limit reached
  if (message.action === "context_limit_reached") {
    const percentage = getTokenUsagePercentage(
      message.current_token_count,
      message.max_token_count,
      message.error_message_exists
    );

    return (
      <div className="bg-[#1111112] border-[#FFFFFF1F] border p-2 rounded-[16px] flex flex-col gap-2">
        <div className="flex items-center justify-between gap-2 p-[10px] rounded-[8px] bg-[#FFAE6614]">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <img
                src={
                  !message.error_message_exists ? AlertOrangeIcon : AlertFillSVG
                }
                alt="Alert"
                className={`w-5 h-5 ${getTokenUsageColor(
                  message.error_message_exists
                )}`}
              />
              <span
                className={getTokenUsageColor(message.error_message_exists)}
              >
                {percentage}%
              </span>
            </div>
            <span className="font-medium">Agent context used</span>
          </div>
          <span className="font-medium text-white">
            <span className={getTokenUsageColor(message.error_message_exists)}>
              {formatTokenCount(
                message.current_token_count,
                message.max_token_count
              )}
              K{" "}
            </span>
            / {formatTokenCount(message.max_token_count)}K tokens
          </span>
        </div>
        <div className="flex items-center justify-between gap-2 text-sm bg-[#FFFFFF0D] rounded-lg">
          <div className="flex flex-col items-start gap-6 p-4 py-5">

            <div className="flex flex-col gap-3">
              <span className="font-medium text-white text-[24px] font-brockmann">
              Summarize & continue in a fresh session
            </span>

            <span className="text-[#737780] text-[15px] leading-[24px] font-[500] text-start font-['Inter']">Continue your work in a new conversation while keeping the important context from this session, perfect for continuing complex projects without losing progress.</span>

            </div>
            
            <div className="flex  justify-between w-full border-[#242424]">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <button
                      type="button"
                      onClick={() => {
                        modalOpen && setModalOpen && setModalOpen({
                          ...modalOpen,
                          fork: !modalOpen.fork,
                        });
                      }}
                      disabled={podIsPaused}
                      className="pl-4 pr-3 py-[10px] h-[40px] bg-white text-black hover:bg-gray-100 rounded-[10px] font-[600] text-[16px] flex items-center gap-2"
                    >
                      Fork Session
                      <img src={ForkIcon} alt="Fork" className="w-5 h-5 invert" />
                    </button>
                  </TooltipTrigger>

                  <TooltipContent
                    className={cn(
                      "bg-[#DDDDE6] text-black border-0",
                      !podIsPaused ? "hidden" : ""
                    )}
                  >
                    <span className="text-[12px] md:text-[14px] font-['Inter']">
                      {podIsPaused ? "Please wake up the agent before forking session" : ""}
                    </span>
                    <TooltipArrow className="fill-[#DDDDE6]" />
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <button
                onClick={() => {
                  window.open(
                      URL_LINKS.forking.learnMore,
                    "_blank"
                  );
                }}
                className="pl-4 pr-3 py-[10px] h-[40px] bg-transparent text-white/70 hover:bg-[#FFFFFF0A] rounded-[10px] font-[500] text-[16px] flex items-center gap-2"
              >
                Learn more about Forking
                <img src={Question} alt="Question" className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};
