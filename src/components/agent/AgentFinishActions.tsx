import AgentBudgetButton from "../AgentBudgetButton";
import <PERSON><PERSON><PERSON> from "@/assets/eye.svg";
import GithubDark from "@/assets/github_dark.svg";

interface AgentFinishActionsProps {
    containerId?: string;
    isCloudFlow?: boolean;
    hideImportantActions?: boolean;
    handleDeploy: () => void;
    handleCopyToHost: () => void;
    handleOpenVsCode: () => void;
    handlePushToGithub: () => void;
    isLoading?: boolean;
    togglePanel: (args: { panelName: string; value?: boolean | null }) => void;
    podIsPaused?: boolean;
}

export const AgentFinishActions = ({
  hideImportantActions = false,
  handlePushToGithub,
  togglePanel,
  podIsPaused = false,
}: AgentFinishActionsProps) => {

  if (hideImportantActions) {
    return null;
  }

  return (
    <div className="flex flex-col w-full space-y-4">
      <div className="w-full max-w-4xl px-2 mx-auto"></div>
      <div className="w-full">
        <div className="flex w-full max-w-4xl gap-4 px-2 pl-0 mx-auto">
          <AgentBudgetButton
            title="Save to github"
            variant="filled"
            className="flex items-center gap-12 bg-green-400 hover:bg-[#25b75b] "
            onClick={handlePushToGithub}
            disabled={podIsPaused}
          >
            <div className="flex items-center justify-center w-full gap-4">
              <span className="font-brockmann text-[16px] leading-[20px] font-semibold tracking-[-0.02em]">
              Save to Github
              </span>
              <img
                src={GithubDark}
                alt="github"
                className="w-5 h-5 text-white"
              />
            </div>
          </AgentBudgetButton>

          <AgentBudgetButton
            title="Push to github"
            variant="filled"
            className="flex items-center gap-12 bg-white hover:bg-[#cfcfcf] "
            onClick={() => {
              togglePanel({
                panelName: "showUrlPreviewPanel",
              });
            }}
            disabled={podIsPaused}
          >
            <div className="flex items-center justify-center w-full gap-4">
              <span className="font-brockmann  text-[16px] leading-[20px] font-semibold">
                Preview
              </span>
              <img src={EyeSVG} alt="github" className="w-5 h-5 text-white" />
            </div>
          </AgentBudgetButton>

          {/* <AgentBudgetButton  title="Push to github" variant="filled" className="flex items-center gap-12 bg-white hover:bg-[#cfcfcf] " onClick={handleDeploy} >
                            <div className="flex items-center justify-center w-full gap-4">
                            <span className="font-brockmann  text-[16px] leading-[20px] font-semibold">Deploy</span>
                            <img src={DeploySVG} alt="github" className="w-5 h-5 text-white" />
                            </div>

                         </AgentBudgetButton> */}
        </div>
      </div>
    </div>
  );
};
