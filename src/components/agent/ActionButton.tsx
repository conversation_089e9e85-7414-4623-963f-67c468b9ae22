import { cn } from "@/lib/utils";
import { ChevronRight, ChevronDown } from "lucide-react";
import { ActionStatus } from "./ActionStatus";
import { ActionDisplay } from "./ActionDisplay";
import { ObservationRenderer } from "./ObservationRenderer";

interface ActionButtonProps {
  message: {
    action?: string;
    observation?: string;
    function_name?: string;
    content: string;
    env_success?: boolean;
  };
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  isSubagent?: boolean;
}

export const ActionButton = ({ 
  message, 
  isExpanded, 
  setIsExpanded, 
  isSubagent = false 
}: ActionButtonProps) => {
  
  // Don't render for these action types
  if (!message.action || 
      message.action === "finish" || 
      message.action === "exit_cost_credit_limit_reached" || 
      message.action === "exit_cost" || 
      message.action === "context_limit_reached" ||
      message.function_name === "rollback" ||
      message.function_name === "ask_human") {
    return null;
  }

  const isRunning : boolean = message.content?.includes("thought:") && Boolean(message.action);

  return (
    <button
      type="button"
      onClick={() => message.observation && setIsExpanded(!isExpanded)}
      className={cn(
        "max-md:max-w-screen w-full max-w-[calc(896px-52px)] text-wrap mt-2 bg-[#1B1B1B] rounded-lg text-left",
        message.observation && "cursor-pointer hover:bg-[#202020]"
      )}
    >
      <div className="flex items-start justify-between p-3">
        <div className="flex items-start flex-1 min-w-0 gap-2">
          <div className="flex items-start gap-2 text-wrap">
            <ActionStatus 
              isRunning={isRunning} 
              envSuccess={message.env_success} 
              action={message.action} 
            />
          </div>
          <div className="flex items-center flex-1 min-w-0 gap-2">
            <span className="text-[#E5E5E5]/20 shrink-0">$</span>
            <ActionDisplay 
              action={message.action}
              functionName={message.function_name}
              isExpanded={isExpanded}
              setIsExpanded={setIsExpanded}
              isSubagent={isSubagent}
            />
          </div>
        </div>
        {message.observation && (
          <div className="text-[#C4C4CC] transition-transform duration-200 ml-2 shrink-0">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </div>
        )}
      </div>
      {isExpanded && (
        <div
          className={cn(
            "grid transition-[grid-template-rows] duration-200",
            isExpanded ? "grid-rows-[1fr]" : "grid-rows-[0fr]"
          )}
        >
          <div className="overflow-hidden">
            <div className="border-t border-[#313131]">
              {message.observation && (
                <ObservationRenderer message={message} />
              )}
            </div>
          </div>
        </div>
      )}
    </button>
  );
};