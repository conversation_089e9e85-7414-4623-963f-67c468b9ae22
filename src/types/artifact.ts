// Artifact upload request interface (Step 1: Get upload URL)
export interface ArtifactUploadRequest {
  file_name: string;
  visibility?: "public" | "private";
  mime_type: string;
}

// Artifact finalize upload request interface (Step 3: Finalize upload)
export interface ArtifactFinalizeRequest {
  entity_id: string;
  entity_type: "job";
  visibility: "public" | "private";
  file_name: string;
  description?: string;
}

// Batch artifact finalize request interface
export interface BatchArtifactFinalizeRequest {
  artifacts: Array<{
    artifact_id: string;
    entity_id: string;
    entity_type: "job";
    visibility: "public" | "private";
    file_name: string;
    description?: string;
    file_path?: string;
  }>;
}

// Artifact upload response interface
export interface ArtifactUploadResponse {
  upload_url: string;
  download_url?: string;
  expires_at: string;
  artifact_id: string;
  file_path: string;
}

// Artifact data interface
export interface Artifact {
  id: string;
  entity_id: string;
  entity_type: "job" | "project";
  visibility: "public" | "private";
  name: string;
  description?: string;
  artifact_type: string;
  public_url: string;
  file_path?: string;
  file_size?: string;
  mime_type?: string;
  created_by: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
}

// File attachment data for upload
export interface FileAttachmentData {
  file: File;
  name: string;
  size: number;
  type: string;
  lastModified: number;
  description?: string;
}

// Upload progress interface
export interface UploadProgress {
  artifactId: string;
  fileName: string;
  progress: number;
  status: "uploading" | "completed" | "failed";
  error?: string;
}
