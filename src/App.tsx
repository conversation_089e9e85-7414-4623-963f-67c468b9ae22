import React from "react";
import { Routes, Route, Navigate, useLocation, useSearchParams } from "react-router-dom";
import { useAuth } from "./contexts";
import ProtectedLayout from "./components/layouts/ProtectedLayout";
import { Spinner } from "./components/ui/spinner";
import { GlobalProgress } from "./components/GlobalProgress";
import { useEffect, useRef, useState } from "react";
import { agentApi, JobResponse } from "./services/agentApi";
import { useTabState } from "./components/TabBar";
import GitHubCallback from "./components/github/GitHubCallback";
import Authentication from "./routes/authentication";
import EmailVerification from "./routes/email-verfication";
import PasswordReset from "./routes/passwordReset";
import PrivacyPolicy from "./routes/privacy-policy";
import { trackFeatureUsage, trackPageView } from "./services/postHogService";
import PaymentStatusModal from "./components/modals/PaymentStatusModal";
import SubscriptionStatusModal from "./components/modals/SubscriptionStatusModal";
import { usePaymentStatus } from "./hooks/usePaymentStatus";
import { initTranslationPrevention } from "./lib/utils/preventTranslation";
import TermsAndService from "./routes/terms-and-service";
import { Toaster } from "./components/ui/toaster";
import PreviewLoading from "./features/preview/PreviewLoading";
import PreviewNotFound from "./features/preview/PreviewNotFound";
import OAuth from "./routes/oauth";


const getChatIdFromPath = (path: string) => {
  const pathParts = path.split("/");
  return pathParts.length >= 3 ? pathParts[2] : null;
};

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, session, loading } = useAuth();
  const { updateTabState, setActiveTab, setTabs, getTabByJobId } =
    useTabState();
  const location = useLocation();
  const [searchParams] = useSearchParams();

  const [processedPaths, setProcessedPaths] = useState<Set<string>>(new Set());
  const currentlyProcessingJob = useRef<string | null>(null);

  // Set active tab to "home" when navigating to root URL
  useEffect(() => {
    if (location.pathname === "/") {
      setActiveTab("home");
    }
  }, [location.pathname, setActiveTab]);

  useEffect(() => {
    if (location.pathname.includes("/chat")) {
      const chatId = getChatIdFromPath(location.pathname);
      if (
        chatId &&
        !processedPaths.has(location.pathname) &&
        currentlyProcessingJob.current === null
      ) {
        const existingTab = getTabByJobId(chatId);

        if (existingTab) {
          //console.log("Existing tab found, activating", existingTab.id);
          setActiveTab(existingTab.id);
          setProcessedPaths((prev) => new Set([...prev, location.pathname]));
        } else {
          currentlyProcessingJob.current = chatId;
          //console.log("Starting job processing", chatId);

          processJob(chatId);
        }
      }
    }
  }, [location.pathname, processedPaths]);

  // Handle job_id query parameter for any route
  useEffect(() => {
    const jobId = searchParams.get("job_id");

    if (jobId && currentlyProcessingJob.current === null) {
      const existingTab = getTabByJobId(jobId);

      if (existingTab) {
        // If a tab for this job already exists, just activate it
        setActiveTab(existingTab.id);

        // Remove the job_id parameter from URL to prevent reopening on refresh
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete("job_id");
        window.history.replaceState(null, "",
          `${location.pathname}${newSearchParams.toString() ? `?${newSearchParams.toString()}` : ""}`);
      } else {
        // Process the job to create a new tab
        currentlyProcessingJob.current = jobId;
        processJob(jobId);
      }
    }
  }, [searchParams, getTabByJobId, setActiveTab]);

  const processJob = async (jobId: string) => {
    try {
      const existingTab = getTabByJobId(jobId);
      if (existingTab) {
        //console.log("Tab was created in the meantime, activating", existingTab.id);
        setActiveTab(existingTab.id);
        setProcessedPaths((prev) => new Set([...prev, location.pathname]));
        currentlyProcessingJob.current = null;
        return;
      }

      const response = await agentApi.getJob(jobId);
      const jobDetails = response.data;

      if (!jobDetails) {
        console.error("No job details found for job ID:", jobId);
        currentlyProcessingJob.current = null;
        return;
      }

      handleJobClick(jobDetails);

      setProcessedPaths((prev) => new Set([...prev, location.pathname]));

      // Remove the job_id parameter from URL after processing
      if (searchParams.has("job_id")) {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete("job_id");
        window.history.replaceState(null, "",
          `${location.pathname}${newSearchParams.toString() ? `?${newSearchParams.toString()}` : ""}`);
      }
    } catch (error) {
      console.error("Error processing job:", error);
    } finally {
      currentlyProcessingJob.current = null;
    }
  };

  const handleJobClick = (job: JobResponse) => {
    if (!job.payload.task) {
      console.error("No task data available");
      return;
    }

    const containerId = job.payload.container_id || job.id;
    if (!containerId) {
      console.error("No container ID available");
      return;
    }

    const existingTab = getTabByJobId(job.id);
    if (existingTab) {
      //console.log("Tab already exists, activating instead of creating", existingTab.id);
      setActiveTab(existingTab.id);

      // Track tab switch
      trackFeatureUsage("tab_switched", {
        jobId: job.id,
        containerId,
        tabId: existingTab.id,
        taskLength: job.payload.task?.length || 0,
      });

      return;
    }

    //console.log("Creating new tab for job", job.id);

    const newTabId = `tab-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    const trajPath = `/root/runs/${job.id}/trajectory.json`;

    const tabState = {
      containerId: job.payload.container_id || job.id,
      initial_commit_id: job.payload.initial_commit_id,
      task: job.payload.task,
      jobId: job.id,
      trajPath,
      tabId: newTabId,
      fromJobList: true,
      isCloudFlow: job.payload?.is_cloud,
      clientRefId: job.client_ref_id,
      modelName: job.payload.model_name,
      promptName: job.payload.prompt_name,
      promptVersion: job.payload.prompt_version,
      costLimit: job.payload.per_instance_cost_limit,
      agentName: job.payload.agent_name,
      portMapping: job.payload.portMapping,
      created_by: job.created_by,
    };

    setTabs((prevTabs) => [
      ...prevTabs,
      {
        id: newTabId,
        title: `${job.payload.task}`,
        path: "/chat",
        state: tabState,
      },
    ]);

    // Track tab opened
    trackFeatureUsage("tab_opened", {
      jobId: job.id,
      containerId,
      tabId: newTabId,
      isCloudFlow: job.payload?.is_cloud || false,
      modelName: job.payload.model_name,
      taskLength: job.payload.task?.length || 0,
    });

    updateTabState(newTabId, tabState);
    setActiveTab(newTabId);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center space-y-4 text-center">
          <Spinner className="mx-auto" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (location.pathname === "/") {
    return children;
  }

  if (location.pathname.includes("/chat")) {
    return children;
  }

  if (!session || !user) {
    return <Navigate to="/" replace />;
  }

  return children;
}

// function ErrorBoundary({ children }: { children: React.ReactNode }) {
//   return children;
// }

function App() {
  const { user } = useAuth();
  const location = useLocation();
  const [searchParams] = useSearchParams();

  // Use the payment status hook to handle payment URL parameters
  const {
    isPaymentModalOpen,
    setPaymentModalOpen,
    isSubscriptionModalOpen,
    setSubscriptionModalOpen,
    paymentId,
    paymentStatus,
    paymentType
  } = usePaymentStatus();

  useEffect(() => {
    if (user && user.user_metadata) {
      if (user.user_metadata.email_verified !== true) {
        // Use navigate with state instead of window.location.href to pass email
        const userEmail = user.email;
        if (userEmail) {
          // Construct URL with email parameter
          window.location.href = `/verify?email=${encodeURIComponent(
            userEmail
          )}`;
        } else {
          window.location.href = "/";
        }
      } else if (
        user.user_metadata.needs_invite_code &&
        location.pathname !== "/activate"
      ) {
        window.location.href = "/activate";
      }
    }
  }, [user, location.pathname]);

  // Initialize translation prevention
  useEffect(() => {
    initTranslationPrevention();
  }, []);

  // Handle mobile query parameter
  useEffect(() => {
    const mobileParam = searchParams.get("mobile");
    if (mobileParam === "true") {
      localStorage.setItem("mobile", "true");
    }
  }, [searchParams]);

  useEffect(() => {
    trackPageView(location.pathname, {
      search: location.search,
      hash: location.hash,
    });
  }, [location]);

  return (

      <div className="h-dvh md:h-screen font-sans antialiased bg-background dark">
        {/* Render the payment status modal if needed */}
        {paymentId && paymentStatus && paymentType === 'payment' && (
          <PaymentStatusModal
            isOpen={isPaymentModalOpen}
            onOpenChange={setPaymentModalOpen}
            paymentId={paymentId}
            initialStatus={paymentStatus}
          />
        )}

        {/* Render the subscription status modal if needed */}
        {paymentId && paymentStatus && paymentType === 'subscription' && (
          <SubscriptionStatusModal
            isOpen={isSubscriptionModalOpen}
            onOpenChange={setSubscriptionModalOpen}
            paymentId={paymentId}
            initialStatus={paymentStatus}
          />
        )}
        <Routes>
          <Route path="/login" element={<Authentication view="login" />} />
          <Route path="/register" element={<Authentication view="signup" />} />
          <Route path="/oauth" element={<OAuth />} />
          <Route
            path="/activate"
            element={<Authentication view="activate" />}
          />
          <Route path="/loading-preview" element={<PreviewLoading />} />
          <Route path="/preview-not-found" element={<PreviewNotFound />} />
          <Route path="/verify" element={<EmailVerification />} />
          <Route path="/reset-password" element={<PasswordReset />} />
          {/* Add GitHub callback route for web environment */}
          <Route path="/github-callback" element={<GitHubCallback />} />
          {/* Legal pages */}
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />
          <Route path="/terms-of-service" element={<TermsAndService />} />
          <Route
            path="*"
            element={
              <ProtectedRoute>
                <ProtectedLayout />
              </ProtectedRoute>
            }
          />
        </Routes>
        <Toaster />
        <GlobalProgress />
      </div>
  );
}

export default App;