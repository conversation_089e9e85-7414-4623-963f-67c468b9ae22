import { useState, useEffect, useCallback } from 'react';
import { useLazyGetJobPreviewUrlQuery } from '@/store/api/apiSlice';


interface UsePreviewUrlsOptions {
  jobId?: string;
  containerId?: string;
  isCloudFlow?: boolean;
  onVSCodeUrlChange?: (url: string) => void;
  onVSCodePasswordChange?: (password: string) => void;
  autoFetch?: boolean;
}

interface UsePreviewUrlsReturn {
  // Data
  previewUrl: string;
  shareableUrl: string;
  vscodeUrl: string;
  password: string;
  basePreviewUrl: string;
  
  // State
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setPreviewUrl: (url: string) => void;
  refresh: () => Promise<void>;
  clearData: () => void;
}

/**
 *  hook for managing preview URLs using RTK Query
 * Just fetches data when needed, no complex Redux state management
 */
export function usePreviewUrls({
  jobId,
  containerId,
  isCloudFlow = false,
  onVSCodeUrlChange,
  onVSCodePasswordChange,
  autoFetch = true,
}: UsePreviewUrlsOptions = {}): UsePreviewUrlsReturn {
  
  // Local state for preview data
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [shareableUrl, setShareableUrl] = useState<string>('');
  const [vscodeUrl, setVscodeUrl] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [basePreviewUrl, setBasePreviewUrl] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // RTK Query hook for fetching preview URL
  const [triggerFetch, { isLoading }] = useLazyGetJobPreviewUrlQuery();


  // Refresh function
  const refresh = useCallback(async () => {
    if (!jobId) return;
    
    try {
      setError(null);
      const result = await triggerFetch(jobId);
      
      if (result.data) {
        const data = result.data;
        setPreviewUrl(data.base_preview_url);
        setShareableUrl(data.preview_url);
        setVscodeUrl(data.vscode_url);
        setPassword(data.password);
        setBasePreviewUrl(data.base_preview_url);
        
        // Call callbacks if provided
        if (onVSCodeUrlChange && data.vscode_url) {
          onVSCodeUrlChange(data.vscode_url);
        }
        
        if (onVSCodePasswordChange && data.password) {
          onVSCodePasswordChange(data.password);
        }
      } else if (result.error) {
        const errorMessage = 'error' in result.error ? String(result.error.error) : 'Failed to fetch preview URL';
        setError(errorMessage);
      }
    } catch (error) {
      console.error('Error refreshing preview URL:', error);
      setError(error instanceof Error ? error.message : 'Failed to refresh preview URL');
    }
  }, [jobId, triggerFetch, onVSCodeUrlChange, onVSCodePasswordChange]);

  // Clear data function
  const clearData = useCallback(() => {
    setPreviewUrl('');
    setShareableUrl('');
    setVscodeUrl('');
    setPassword('');
    setBasePreviewUrl('');
    setError(null);
  }, []);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch && jobId && !previewUrl && !isLoading) {
      refresh();
    }
  }, [autoFetch, jobId, previewUrl, isLoading, refresh]);

  return {
    // Data
    previewUrl,
    shareableUrl,
    vscodeUrl,
    password,
    basePreviewUrl,
    
    // State
    isLoading,
    error,
    
    // Actions
    setPreviewUrl,
    refresh,
    clearData,
  };
}
