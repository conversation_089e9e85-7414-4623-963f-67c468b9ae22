import { useToast } from "@/hooks/use-toast";
import { agent<PERSON><PERSON> } from "@/services/agentApi";
import { EnvValues } from "@/store/deploySlice";

interface UseDeploymentActionsProps {
  jobId?: string;
  agentStatus?: string;
  handleDeploy: ({ image, deployment_id, acknowledgements, db_mode }: { image?: string; deployment_id?: string; acknowledgements?: string, db_mode?: boolean }) => Promise<void>;
  checkDeployStatus: (jobId: string, silent: boolean) => Promise<void>;
  updateEnvironmentVariables?: (envValues: EnvValues[]) => Promise<boolean>;
  selectedReplacement?: { deployment_id: string } | null;
  setModalStatus: (status: any) => void;
  modalStatus: any;
  setIsDeploymentTransitioning: (value: boolean) => void;
  setLocalLoading: (value: boolean) => void;
}

export function useDeploymentActions({
  jobId,
  agentStatus,
  handleDeploy,
  checkDeployStatus,
  updateEnvironmentVariables,
  selectedReplacement,
  setModalStatus,
  modalStatus,
  setIsDeploymentTransitioning,
  setLocalLoading,
}: UseDeploymentActionsProps) {
  const { toast } = useToast();

  const handleReplacement = async (db_mode: boolean) => {
    if (agentStatus === "running") {
      toast({
        title: "Agent is Running",
        description:
          "Please wait for agent to finish or stop it manually before starting the deployment",
        variant: "destructive",
        duration: 2000,
      });
      return;
    }

    // Open the replace deployment confirmation modal
    setModalStatus({
      ...modalStatus,
      confirmReplaceDeploymentModal: false,
    });

    try {
      if (jobId) {
        try {
          // Set both loading states to prevent UI flickering
          setLocalLoading(true);
          setIsDeploymentTransitioning(true);

          // Close the modal
          setModalStatus({
            ...modalStatus,
            confirmReplaceDeploymentModal: false,
          });

          // Initiate deployment
          await handleDeploy({ deployment_id: selectedReplacement?.deployment_id, db_mode });

          // ISSUE FIX: Don't immediately check status after deployment
          // The handleDeploy function already starts polling if needed
          console.log(`[useDeploymentActions] Skipping immediate checkDeployStatus call - letting polling handle it`);

          // if (jobId) {
          //   setTimeout(() => {
          //     checkDeployStatus(jobId, false);
          //   }, 100);
          // }
        } catch (error) {
          console.error("Error in handleDeploy:", error);
          // Only retry if the deployment itself failed, not status checking
          // await handleDeploy();

          // if (jobId) {
          //   checkDeployStatus(jobId, false);
          // }
        } finally {
          // Clear loading state after a delay to ensure smooth transition
          setTimeout(() => {
            setLocalLoading(false);
            setIsDeploymentTransitioning(false);
          }, 500);
        }
      }
    } catch (error: any) {
      toast({
        title: "Deployment Failed",
        description: "Failed to deploy. Please try again.",
        variant: "destructive",
        duration: 2000,
      });
      setLocalLoading(false);
      setIsDeploymentTransitioning(false);

      // ISSUE FIX: Don't immediately check status after deployment error
      // if (jobId) {
      //   checkDeployStatus(jobId, false);
      // }
    }
  };

  // Function to execute the actual redeployment after confirmation
  const executeRedeployment = async (
    envSyncedWithProduction: boolean,
    envValues: EnvValues[],
    setEnvSyncedWithProduction: (value: boolean) => void,
    acknowledgements?: string
  ) => {
    try {
      if (jobId) {
        try {
          // Set both loading states to prevent UI flickering
          setLocalLoading(true);
          setIsDeploymentTransitioning(true);

          // Close the modal
          setModalStatus({
            ...modalStatus,
            redeployModal: false,
          });

          if (!envSyncedWithProduction && envValues.length > 0) {
            try {
              console.log("Updating environment variables before redeployment");

              // Use the updateEnvironmentVariables function from useDeploy
              // This ensures Redux state is properly updated
              if (updateEnvironmentVariables) {
                const success = await updateEnvironmentVariables(envValues);
                if (success) {
                  setEnvSyncedWithProduction(true);
                }
              } else {
                // Fallback to direct API call if the hook function is not available
                await agentApi.updateEnvironmentVariables(jobId, envValues);
                setEnvSyncedWithProduction(true);
              }
            } catch (error) {
              console.error("Error updating environment variables:", error);
            }
          }

          // Initiate deployment
          await handleDeploy({ acknowledgements });

          // ISSUE FIX: Don't immediately check status after deployment
          // The handleDeploy function already starts polling if needed
          console.log(`[useDeploymentActions] executeRedeployment - Skipping immediate checkDeployStatus call`);

          // if (jobId) {
          //   setTimeout(() => {
          //     checkDeployStatus(jobId, false);
          //   }, 100);
          // }
        } catch (error) {
          console.error("Error in handleDeploy:", error);
          // Only retry if the deployment itself failed, not status checking
          // await handleDeploy();

          // if (jobId) {
          //   checkDeployStatus(jobId, false);
          // }
        } finally {
          // Clear loading state after a delay to ensure smooth transition
          setTimeout(() => {
            setLocalLoading(false);
            setIsDeploymentTransitioning(false);
          }, 500);
        }
      } else {
        await handleDeploy({ acknowledgements });
      }
    } catch (error: any) {
      toast({
        title: "Deployment Failed",
        description: "Failed to deploy. Please try again.",
        variant: "destructive",
        duration: 2000,
      });
      setLocalLoading(false);
      setIsDeploymentTransitioning(false);

      // ISSUE FIX: Don't immediately check status after deployment error
      // if (jobId) {
      //   checkDeployStatus(jobId, false);
      // }
    }
  };

  return {
    handleReplacement,
    executeRedeployment,
  };
}
