import { useState } from 'react';
import { agent<PERSON>pi } from '@/services/agentApi';
import { useToast } from './use-toast';
import { useCredits } from '@/contexts';

// Define the credit bundle type
export interface CreditBundle {
  amount: number;
  price: number;
  selected: boolean;
}

export interface SubscriptionPackage{
  id: string;
  name: string;
  amount: number;
  credits: number;
}

// Default credit bundles
export const DEFAULT_CREDIT_BUNDLES: CreditBundle[] = [
  {
    amount: 100,
    price: 20,
    selected: false,
  },
  {
    amount: 250,
    price: 50,
    selected: true,
  },
  {
    amount: 500,
    price: 100,
    selected: false,
  },
];

// Pro subscription plan ID
export const PRO_SUBSCRIPTION_PLAN_ID = "5d98824c-26c1-4dc4-822a-007ad1d5e684";

export function usePayment() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [processingBundleAmount, setProcessingBundleAmount] = useState<number | null>(null);
  const [isUpgradeLoading, setIsUpgradeLoading] = useState(false);
  const [bundles, setBundles] = useState<CreditBundle[]>(DEFAULT_CREDIT_BUNDLES);

    const {upgradeDetails} = useCredits();

  // Handle bundle selection
  const handleBundleSelect = (selectedAmount: number) => {
    setBundles(bundles.map((bundle) => ({
      ...bundle,
      selected: bundle.amount === selectedAmount
    })));
  };

  // Get the selected bundle
  const getSelectedBundle = () => bundles.find((b) => b.selected);

  // Handle payment button click for buying credits
  const handleBuyCredits = async (bundle: CreditBundle) => {
    setIsLoading(true);
    setProcessingBundleAmount(bundle.amount);
    try {
      const { url } = await agentApi.createCheckoutSession(bundle.price);
      if (url) {
        window.location.href = url;
      } else {
        toast({
          title: "Error",
          description: "Failed to create checkout session",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);
      toast({
        title: "Error",
        description: "Failed to create checkout session",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setProcessingBundleAmount(null);
    }
  };
  
  // Handle upgrade to Pro tier
  const handleUpgradeToPro = async () => {
    setIsUpgradeLoading(true);
    try {

      if(!upgradeDetails?.id) {
        throw new Error("No Subscription available at this moment for this account");
      }
      // Use the checkout session API with subscription mode and plan ID
      const { url } = await agentApi.createCheckoutSession(0, true, upgradeDetails?.id);
      if (url) {
        window.location.href = url;
      } else {
        toast({
          title: "Error",
          description: "Failed to create checkout session",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to create checkout session:', error);
      toast({
        title: "Error",
        description: "Failed to create checkout session",
        variant: "destructive",
      });
    } finally {
      setIsUpgradeLoading(false);
    }
  };

  // Handle managing subscription (edit billing)
  const handleManageSubscription = async () => {
    setIsUpgradeLoading(true);
    try {
      const response = await agentApi.getManageSubscriptionUrl();
      if (response && response.url) {
        window.location.href = response.url;
      } else {
        toast({
          title: "Error",
          description: "Failed to get subscription management URL",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error getting subscription management URL:", error);
      toast({
        title: "Error",
        description: "Failed to get subscription management URL",
        variant: "destructive",
      });
    } finally {
      setIsUpgradeLoading(false);
    }
  };

  return {
    isLoading,
    processingBundleAmount,
    isUpgradeLoading,
    bundles,
    setBundles,
    handleBundleSelect,
    getSelectedBundle,
    handleBuyCredits,
    handleUpgradeToPro,
    handleManageSubscription,
  };
}
