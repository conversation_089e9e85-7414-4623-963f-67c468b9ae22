import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { FileAttachmentData, UploadProgress } from '@/types/artifact';
import { agentApi } from '@/services/agentApi';

interface UseFileAttachmentsOptions {
  maxFiles?: number;
  maxSizeInMB?: number;
  allowedTypes?: string[];
  jobId?: string;
  onUploadComplete?: (artifactId: string, fileName: string) => void;
  onUploadError?: (fileName: string, error: string) => void;
}

/**
 * A hook for handling file attachments with validation, upload progress, and artifact management
 */
export function useFileAttachments({
  maxFiles = 10,
  maxSizeInMB = 50,
  allowedTypes = [],
  jobId,
  onUploadComplete,
  onUploadError
}: UseFileAttachmentsOptions = {}) {
  const [files, setFiles] = useState<FileAttachmentData[]>([]);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  /**
   * Validate file size and type
   */
  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      return `File size exceeds ${maxSizeInMB}MB limit`;
    }

    // Check file type if allowedTypes is specified
    if (allowedTypes.length > 0) {
      const isAllowed = allowedTypes.some(type => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        return file.type.toLowerCase().includes(type.toLowerCase());
      });
      
      if (!isAllowed) {
        return `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`;
      }
    }

    return null;
  }, [maxSizeInMB, allowedTypes]);

  /**
   * Process and validate selected files
   */
  const processFiles = useCallback((fileList: FileList | File[] | null) => {
    if (!fileList) return;

    const filesArray = Array.from(fileList);
    const validFiles: FileAttachmentData[] = [];
    const errors: string[] = [];

    // Check total file count
    if (files.length + filesArray.length > maxFiles) {
      errors.push(`Cannot add more than ${maxFiles} files`);
    }

    filesArray.forEach((file) => {
      const validationError = validateFile(file);
      if (validationError) {
        errors.push(`${file.name}: ${validationError}`);
      } else {
        validFiles.push({
          file,
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
        });
      }
    });

    // Show errors if any
    if (errors.length > 0) {
      toast({
        title: "File Validation Error",
        description: errors.join('\n'),
        variant: "destructive",
      });
    }

    // Add valid files
    if (validFiles.length > 0) {
      setFiles(prev => [...prev, ...validFiles]);
    }
  }, [files.length, maxFiles, validateFile, toast]);

  /**
   * Handle file selection from input
   */
  const handleFileSelect = useCallback((fileList: FileList | File[] | null) => {
    processFiles(fileList);
  }, [processFiles]);

  /**
   * Create and return a file input ref for file selection
   */
  const createFileInputRef = useCallback(() => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = true;
    fileInput.style.display = 'none';
    
    // Set accept attribute based on allowedTypes
    if (allowedTypes.length > 0) {
      fileInput.accept = allowedTypes.join(',');
    }

    fileInput.onchange = (e) => {
      const target = e.target as HTMLInputElement;
      handleFileSelect(target.files);
      // Clean up the input after use
      document.body.removeChild(fileInput);
    };

    return fileInput;
  }, [allowedTypes, handleFileSelect]);

  /**
   * Open file picker dialog
   */
  const openFilePicker = useCallback(() => {
    const fileInput = createFileInputRef();
    
    // Append to body temporarily
    document.body.appendChild(fileInput);
    
    // Use setTimeout to ensure the input is properly attached before clicking
    setTimeout(() => {
      fileInput.click();
    }, 0);
  }, [createFileInputRef]);

  /**
   * Upload a single file using the new 3-step flow
   */
  const uploadFile = useCallback(async (fileData: FileAttachmentData & { description?: string }): Promise<void> => {
    if (!jobId) {
      throw new Error('Job ID is required for file upload');
    }

    const progressId = `${fileData.name}-${Date.now()}`;

    // Initialize upload progress
    setUploadProgress(prev => [...prev, {
      artifactId: progressId,
      fileName: fileData.name,
      progress: 0,
      status: 'uploading'
    }]);

    try {
      // Step 1: Get upload URL from backend
      const uploadRequest = {
        file_name: fileData.name,
        mime_type: fileData.type,
      };

      const uploadResponse = await agentApi.uploadArtifact(jobId, uploadRequest);

      // Update progress after getting upload URL
      setUploadProgress(prev => prev.map(p =>
        p.artifactId === progressId
          ? { ...p, progress: 33, artifactId: uploadResponse.artifact_id, file_path: uploadResponse.file_path }
          : p
      ));

      // Step 2: Upload file to the provided URL
      await agentApi.uploadFileToUrl(uploadResponse.upload_url, fileData.file);

      // Update progress after file upload
      setUploadProgress(prev => prev.map(p =>
        p.artifactId === uploadResponse.artifact_id
          ? { ...p, progress: 66 }
          : p
      ));

      // Step 3: Finalize upload with description and metadata
      const finalizeRequest = {
        entity_id: jobId,
        entity_type: 'job' as const,
        visibility: 'public' as const,
        file_name: fileData.name,
        description: fileData.description || undefined,
        file_path: uploadResponse.file_path
      };

      await agentApi.finalizeArtifactUpload(uploadResponse.artifact_id, finalizeRequest);

      // Update progress to completed
      setUploadProgress(prev => prev.map(p =>
        p.artifactId === uploadResponse.artifact_id
          ? { ...p, progress: 100, status: 'completed' as const }
          : p
      ));

      onUploadComplete?.(uploadResponse.artifact_id, fileData.name);

      toast({
        title: "Upload Complete",
        description: `${fileData.name} uploaded successfully`,
      });

    } catch (error: any) {
      // Update progress to failed
      setUploadProgress(prev => prev.map(p =>
        p.artifactId === progressId
          ? { ...p, status: 'failed' as const, error: error.message }
          : p
      ));

      onUploadError?.(fileData.name, error.message);

      toast({
        title: "Upload Failed",
        description: `Failed to upload ${fileData.name}: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [jobId, onUploadComplete, onUploadError, toast]);

  /**
   * Upload all selected files with batch finalization
   */
  const uploadAllFiles = useCallback(async (files: FileAttachmentData[]) => {
    if (files.length === 0) {
      toast({
        title: "No Files",
        description: "Please select files to upload",
        variant: "destructive",
      });
      return;
    }

    if (!jobId) {
      toast({
        title: "Error",
        description: "Job ID is required for file upload",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const artifactsToFinalize: Array<{
        artifact_id: string;
        entity_id: string;
        entity_type: "job";
        visibility: "public" | "private";
        file_name: string;
        description?: string;
        file_path?: string;
      }> = [];

      // Step 1 & 2: Upload all files (get URLs and upload to cloud)
      for (const fileData of files) {
        const progressId = `${fileData.name}-${Date.now()}`;

        // Initialize upload progress
        setUploadProgress(prev => [...prev, {
          artifactId: progressId,
          fileName: fileData.name,
          progress: 0,
          status: 'uploading'
        }]);

        try {
          // Step 1: Get upload URL from backend
          const uploadRequest = {
            file_name: fileData.name,
            mime_type: fileData.type,
          };

          const uploadResponse = await agentApi.uploadArtifact(jobId, uploadRequest);

          // Update progress after getting upload URL
          setUploadProgress(prev => prev.map(p =>
            p.artifactId === progressId
              ? { ...p, progress: 25, artifactId: uploadResponse.artifact_id, file_path: uploadResponse.file_path }
              : p
          ));

          // Step 2: Upload file to the provided URL
          await agentApi.uploadFileToUrl(uploadResponse.upload_url, fileData.file);

          // Update progress after file upload
          setUploadProgress(prev => prev.map(p =>
            p.artifactId === uploadResponse.artifact_id
              ? { ...p, progress: 50 }
              : p
          ));

          // Collect artifact info for batch finalization
          artifactsToFinalize.push({
            artifact_id: uploadResponse.artifact_id,
            entity_id: jobId,
            entity_type: 'job',
            visibility: 'public',
            file_name: fileData.name,
            description: fileData.description || undefined,
            file_path: uploadResponse.file_path
          });

        } catch (error: any) {
          // Update progress to failed for this specific file
          setUploadProgress(prev => prev.map(p =>
            p.artifactId === progressId
              ? { ...p, status: 'failed' as const, error: error.message }
              : p
          ));

          onUploadError?.(fileData.name, error.message);

          toast({
            title: "Upload Failed",
            description: `Failed to upload ${fileData.name}: ${error.message}`,
            variant: "destructive",
          });
        }
      }

      // Step 3: Batch finalize all successfully uploaded artifacts
      if (artifactsToFinalize.length > 0) {
        try {
          await agentApi.batchFinalizeArtifactUpload({ artifacts: artifactsToFinalize });

          // Update progress to completed for all finalized artifacts
          setUploadProgress(prev => prev.map(p => {
            const artifact = artifactsToFinalize.find(a => a.artifact_id === p.artifactId);
            return artifact
              ? { ...p, progress: 100, status: 'completed' as const }
              : p;
          }));

          // Call onUploadComplete for each successfully uploaded file
          artifactsToFinalize.forEach(artifact => {
            onUploadComplete?.(artifact.artifact_id, artifact.file_name);
          });

          toast({
            title: "Upload Complete",
            description: `${artifactsToFinalize.length} file(s) uploaded successfully`,
          });

        } catch (error: any) {
          // Mark all artifacts as failed if batch finalization fails
          setUploadProgress(prev => prev.map(p => {
            const artifact = artifactsToFinalize.find(a => a.artifact_id === p.artifactId);
            return artifact
              ? { ...p, status: 'failed' as const, error: error.message }
              : p;
          }));

          toast({
            title: "Finalization Failed",
            description: `Failed to finalize uploads: ${error.message}`,
            variant: "destructive",
          });
        }
      }

      // Clear files after upload attempt
      setFiles([]);

    } catch (error) {
      console.error('Error uploading files:', error);
      toast({
        title: "Upload Failed",
        description: "Failed to upload files. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  }, [files, jobId, onUploadComplete, onUploadError, toast]);

  /**
   * Remove a file by index
   */
  const removeFile = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  /**
   * Clear all files
   */
  const clearFiles = useCallback(() => {
    setFiles([]);
    setUploadProgress([]);
  }, []);

  /**
   * Clear completed uploads from progress
   */
  const clearCompletedUploads = useCallback(() => {
    setUploadProgress(prev => prev.filter(p => p.status !== 'completed'));
  }, []);

  return {
    files,
    uploadProgress,
    isProcessing,
    handleFileSelect,
    openFilePicker,
    uploadFile,
    uploadAllFiles,
    removeFile,
    clearFiles,
    clearCompletedUploads,
    hasFiles: files.length > 0,
    hasUploads: uploadProgress.length > 0,
  };
}
