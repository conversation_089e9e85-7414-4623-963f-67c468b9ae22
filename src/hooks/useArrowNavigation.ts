import { useEffect, useCallback } from 'react';

interface UseArrowNavigationProps {
  onNext: () => void;
  onPrevious: () => void;
  disabled?: boolean;
}

/**
 * Custom hook for handling arrow key navigation
 * @param onNext - Function to call when right arrow or down arrow is pressed
 * @param onPrevious - Function to call when left arrow or up arrow is pressed
 * @param disabled - Whether to disable the navigation
 */
export const useArrowNavigation = ({ 
  onNext, 
  onPrevious, 
  disabled = false 
}: UseArrowNavigationProps) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (disabled) return;
    
    // Only handle arrow keys
    if (event.key === 'ArrowRight' || event.key === 'ArrowDown') {
      event.preventDefault();
      onNext();
    } else if (event.key === 'ArrowLeft' || event.key === 'ArrowUp') {
      event.preventDefault();
      onPrevious();
    }
  }, [onNext, onPrevious, disabled]);

  useEffect(() => {
    if (disabled) return;

    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, disabled]);
};
