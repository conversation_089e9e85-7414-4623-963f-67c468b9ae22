import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { agentApi } from "@/services/agentApi";
import { IDeployment } from "@/components/DeployedCard";

interface ModalStatus {
  deployModal: boolean;
  shutDownModal: boolean;
  instantRollbackModal: boolean;
  unlinkDomainModal: boolean;
  redeployModal: boolean;
  replaceDeploymentModal: boolean;
  confirmReplaceDeploymentModal: boolean;
  buildLogsModal: boolean;
}

interface UseDeploymentModalsProps {
  jobId?: string;
  agentStatus?: string;
  checkDeployStatus: (jobId: string, silent: boolean) => Promise<void>;
  isOpen?: boolean; // Add isOpen prop to track when panel opens
  deployStatus?: string; // Add deployStatus to watch for changes
}

export function useDeploymentModals({
  jobId,
  agentStatus,
  checkDeployStatus,
  isOpen,
  deployStatus,
}: UseDeploymentModalsProps) {
  const { toast } = useToast();

  const [modalStatus, setModalStatus] = useState<ModalStatus>({
    deployModal: false,
    shutDownModal: false,
    instantRollbackModal: false,
    unlinkDomainModal: false,
    redeployModal: false,
    replaceDeploymentModal: false,
    confirmReplaceDeploymentModal: false,
    buildLogsModal: false,
  });

  // State for domain unlinking
  const [domainToUnlink, setDomainToUnlink] = useState<string>("");

  // State for replace deployment modal
  const [replaceDeployments, setReplaceDeployments] = useState<IDeployment[]>([]);
  const [replaceHoveredCard, setReplaceHoveredCard] = useState<string | null>(null);
  const [selectedReplacement, setSelectedReplacement] = useState<IDeployment | null>(null);
  const [loadingReplaceDeployments, setLoadingReplaceDeployments] = useState(false);

  // State to track transition between deployments
  const [isDeploymentTransitioning, setIsDeploymentTransitioning] = useState(false);
  const [rollbackTargetId, setRollbackTargetId] = useState<string | null>(null);

  // Effect to reset replace deployment state when modal closes
  useEffect(() => {
    if (!modalStatus.replaceDeploymentModal) {
      setReplaceHoveredCard(null);
      setLoadingReplaceDeployments(false);
    }
  }, [modalStatus.replaceDeploymentModal]);

  // Function to fetch deployments for replacement
  const fetchReplaceDeployments = async () => {
    try {
      setLoadingReplaceDeployments(true);
      const response = await agentApi.getDeployments();

      if ('error' in response) {
        setReplaceDeployments([]);
      } else {
        // Map API response to component's expected format
        const mappedDeployments: IDeployment[] = response.map((deployment: any) => ({
          id: deployment.job_id,
          app_name: deployment.app_name || 'Unnamed App',
          deployStatus: deployment.status,
          deployUrl: deployment.deployed_url || '',
          custom_domain: deployment.custom_domain || '',
          latest_run: {
            status: deployment.status,
            updated_at: deployment.deployed_at || null,
          },
          description: deployment.task || 'No task available',
          deployment_id: deployment.deployment_id,
        }));


        // Filter only successful deployments and exclude current job
        const successfulDeployments = mappedDeployments.filter(
          (deployment) => deployment.id !== jobId
        );

        setReplaceDeployments(successfulDeployments);
      }
    } catch (err: any) {
      console.error('Error fetching replace deployments:', err);
      setReplaceDeployments([]);
    } finally {
      setLoadingReplaceDeployments(false);
    }
  };

  useEffect(() => {
    fetchReplaceDeployments();
  }, []);

  // Refetch replace deployments when panel opens
  useEffect(() => {
    if (isOpen) {
      console.log('[useDeploymentModals] Panel opened, refetching replace deployments');
      fetchReplaceDeployments();
    }
  }, [isOpen]);

  // Refetch replace deployments when deployment status changes
  useEffect(() => {
    if (deployStatus) {
      console.log('[useDeploymentModals] Deploy status changed to:', deployStatus, 'refetching replace deployments');
      fetchReplaceDeployments();
    }
  }, [deployStatus]);

  const handleDeployClick = async () => {
    if (agentStatus === "running") {
      toast({
        title: "Agent is Running",
        description:
          "Please wait for agent to finish or stop it manually before starting the deployment",
        variant: "destructive",
        duration: 2000,
      });
      return;
    }
    setModalStatus({
      ...modalStatus,
      deployModal: true,
    });
  };

  const handleShutDown = () => {
    setModalStatus({
      ...modalStatus,
      shutDownModal: true,
    });
  };

  const handleReDeploy = () => {
    if (agentStatus === "running") {
      toast({
        title: "Agent is Running",
        description:
          "Please wait for agent to finish or stop it manually before starting the deployment",
        variant: "destructive",
        duration: 2000,
      });
      return;
    }

    setModalStatus({
      ...modalStatus,
      redeployModal: true,
    });
  };

  // Handler for opening the unlink domain modal
  const handleOpenUnlinkDomainModal = (domain: string) => {
    setDomainToUnlink(domain);
    setModalStatus({
      ...modalStatus,
      unlinkDomainModal: true,
    });
  };

  // Handler for confirming domain unlinking
  const confirmUnlinkDomain = async () => {
    if (!domainToUnlink || !jobId) return;

    try {
      await agentApi.unlinkDomain(domainToUnlink, jobId);

      setModalStatus({
        ...modalStatus,
        unlinkDomainModal: false,
      });

      toast({
        title: "Domain Unlinked",
        description: "Your domain has been successfully unlinked from this app",
        duration: 3000,
      });

      setDomainToUnlink("");

      if (jobId) {
        checkDeployStatus(jobId, false);
      }
    } catch (error: any) {
      console.error("Domain unlinking error:", error);

      let errorMessage = "Failed to unlink domain";
      if (error?.message) {
        errorMessage = error.message;
      }

      if (error?.response?.data) {
        const data = error.response.data;
        if (data.detail) errorMessage = data.detail;
      }

      toast({
        title: "Unlinking Failed",
        description: errorMessage,
        variant: "destructive",
        duration: 3000,
      });

      setModalStatus({
        ...modalStatus,
        unlinkDomainModal: false,
      });
    }
  };

  return {
    modalStatus,
    setModalStatus,
    domainToUnlink,
    setDomainToUnlink,
    replaceDeployments,
    replaceHoveredCard,
    setReplaceHoveredCard,
    selectedReplacement,
    setSelectedReplacement,
    loadingReplaceDeployments,
    isDeploymentTransitioning,
    setIsDeploymentTransitioning,
    rollbackTargetId,
    setRollbackTargetId,
    handleDeployClick,
    handleShutDown,
    handleReDeploy,
    handleOpenUnlinkDomainModal,
    confirmUnlinkDomain,
    fetchReplaceDeployments,
  };
}
