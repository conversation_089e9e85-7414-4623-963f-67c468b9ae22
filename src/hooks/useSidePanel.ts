import { useState } from "react";

export function useSidePanelState() {
    const [panelState, setPanelState] = useState({
        showLogsPanel: false,
        showSubagentPanel: false,
        showInfoPanel: false,
        showUrlPreviewPanel: false,
        showDeployPanel: false,
    });

    const togglePanel = ({
        panelName,
        value = null,
        size = null
    }: {
        panelName: keyof typeof panelState,
        value?: boolean | null,
        size?: number | null
    }) => {
        setPanelState(prevState => {
            if (value !== null) {
                const allClosed = {
                    showLogsPanel: false,
                    showSubagentPanel: false,
                    showInfoPanel: false,
                    showUrlPreviewPanel: false,
                    showDeployPanel: false,
                };
                return { ...allClosed, [panelName]: value };
            }

            if (prevState[panelName]) {
                return { ...prevState, [panelName]: false };
            }

            const allClosed = {
                showLogsPanel: false,
                showSubagentPanel: false,
                showInfoPanel: false,
                showUrlPreviewPanel: false,
                showDeployPanel: false,
            };
            return { ...allClosed, [panelName]: true };
        });
    };

    const isAnyPanelOpen = () => {
        return Object.values(panelState).some((value) => value === true);
    };

    const closeAllPanels = () => {
        setPanelState({
            showLogsPanel: false,
            showSubagentPanel: false,
            showInfoPanel: false,
            showUrlPreviewPanel: false,
            showDeployPanel: false,
        });
    };

    return {
        ...panelState,
        panelState,
        togglePanel,
        isAnyPanelOpen,
        closeAllPanels
    };
}