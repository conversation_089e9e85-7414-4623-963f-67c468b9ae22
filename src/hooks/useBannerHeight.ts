import { useEffect, useRef } from 'react';
import { useAlert } from '@/contexts/AlertContext';

/**
 * Hook to measure and track banner height changes
 * Should be used in the component that renders the AlertBanner
 */
export const useBannerHeight = () => {
  const { setBannerHeight, bannerConfig } = useAlert();
  const bannerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  useEffect(() => {
    const bannerElement = bannerRef.current;
    
    if (!bannerElement) return;

    // Function to update height
    const updateHeight = () => {
      if (bannerConfig.show && bannerElement) {
        const height = bannerElement.offsetHeight;
        setBannerHeight(height);
      } else {
        setBannerHeight(0);
      }
    };

    // Initial height measurement
    updateHeight();

    // Use ResizeObserver to watch for size changes
    if (window.ResizeObserver) {
      resizeObserverRef.current = new ResizeObserver(() => {
        updateHeight();
      });
      
      resizeObserverRef.current.observe(bannerElement);
    }

    // Cleanup
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [bannerConfig.show, setBannerHeight]);

  // Update height when banner visibility changes
  useEffect(() => {
    if (!bannerConfig.show) {
      setBannerHeight(0);
    }
  }, [bannerConfig.show, setBannerHeight]);

  return bannerRef;
};
