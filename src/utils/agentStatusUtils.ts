export type AgentStatus = 
  | "running"
  | "waiting"
  | "stopped"
  | "stopping"
  | "subagent_running"
  | "subagent_waiting"
  | "subagent_stopping"
  | "forking";

export interface AgentState {
  agent_running: boolean;
  job_running: boolean;
}

export interface AgentStatusParams {
  agentState?: AgentState | null;
  isSubagentActive?: boolean;
  currentAgentStatus?: AgentStatus;
  isPauseLoading?: boolean;
  forked_status?: "running" | "success" | "failed" | null;
}

/**
 * Calculates the current agent status based on various state parameters
 * This utility centralizes the agent status calculation logic used across components
 */
export const calculateAgentStatus = ({
  agentState,
  isSubagentActive = false,
  currentAgentStatus = "stopped",
  isPauseLoading = false,
  forked_status = null,
}: AgentStatusParams): AgentStatus => {
  // If external pause loading is active, show stopping state
  if (isPauseLoading) {
    return isSubagentActive ? "subagent_stopping" : "stopping";
  }

  // Check if subagent is active first
  if (isSubagentActive) {
    // If agent is not running but subagent is active, it's waiting for human response
    if (!agentState?.agent_running && !agentState?.job_running) {
      // If we were in the stopping state, we've now stopped
      if (currentAgentStatus === "subagent_stopping") {
        return "subagent_waiting";
      }
      return "subagent_waiting";
    }

    // Preserve the stopping state if we're already in it
    if (currentAgentStatus === "subagent_stopping") {
      return "subagent_stopping";
    }

    return "subagent_running";
  }

  // Handle main agent stopping state
  if (currentAgentStatus === "stopping") {
    return !agentState?.agent_running && !agentState?.job_running
      ? "waiting"
      : "stopping";
  }

  // Check if agent is running
  if (agentState?.agent_running || agentState?.job_running) {
    return "running";
  }

  // Check if forking is in progress
  if (forked_status === "running") {
    return "forking";
  }

  // Default to waiting state
  return "waiting";
};

/**
 * Simplified version for components that don't need all the complex logic
 * Used in AgentMessageItem.tsx
 */
export const calculateSimpleAgentStatus = ({
  agentState,
  isSubagentActive = false,
  currentAgentStatus = "waiting",
}: Pick<AgentStatusParams, 'agentState' | 'isSubagentActive' | 'currentAgentStatus'>): AgentStatus => {
  if (isSubagentActive) {
    // If agent is not running but subagent is active, it's waiting for human response
    if (!agentState?.agent_running && !agentState?.job_running) {
      // If we were in the stopping state, we've now stopped
      if (currentAgentStatus === "subagent_stopping") {
        return "subagent_waiting";
      }
      return "subagent_waiting";
    }

    // Preserve the stopping state if we're already in it
    if (currentAgentStatus === "subagent_stopping") {
      return "subagent_stopping";
    }

    return "subagent_running";
  } else {
    if (agentState?.agent_running || agentState?.job_running) {
      return "running";
    } else {
      return "waiting";
    }
  }
};
