import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './index';
import { GitHubInstallation } from './api/apiSlice';

export interface GitHubState {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  
  // User data
  primaryAccount: string | null;
  installations: GitHubInstallation[];
  
  // UI state
  authWindowOpen: boolean;
  lastConnectionCheck: number;
  
  // Error handling
  connectionError: string | null;
}

const initialState: GitHubState = {
  isConnected: false,
  isConnecting: false,
  primaryAccount: null,
  installations: [],
  authWindowOpen: false,
  lastConnectionCheck: 0,
  connectionError: null,
};

export const githubSlice = createSlice({
  name: 'github',
  initialState,
  reducers: {
    // Connection state management
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
      state.isConnecting = false;
      state.connectionError = null;
      state.lastConnectionCheck = Date.now();
    },
    
    setConnecting: (state, action: PayloadAction<boolean>) => {
      state.isConnecting = action.payload;
      if (action.payload) {
        state.connectionError = null;
      }
    },
    
    setConnectionError: (state, action: PayloadAction<string | null>) => {
      state.connectionError = action.payload;
      state.isConnecting = false;
    },
    
    // User data management
    setPrimaryAccount: (state, action: PayloadAction<string | null>) => {
      state.primaryAccount = action.payload;
    },
    
    setInstallations: (state, action: PayloadAction<GitHubInstallation[]>) => {
      state.installations = action.payload;
      // Mark primary installation
      if (state.primaryAccount) {
        state.installations = state.installations.map(installation => ({
          ...installation,
          isPrimary: installation.account_login === state.primaryAccount
        }));
      }
    },
    
    // Auth window management
    setAuthWindowOpen: (state, action: PayloadAction<boolean>) => {
      state.authWindowOpen = action.payload;
    },
    
    // Reset state (for logout)
    resetGitHubState: () => {
      return initialState;
    },
    
    // Update connection check timestamp
    updateLastConnectionCheck: (state) => {
      state.lastConnectionCheck = Date.now();
    },
  },
});

export const {
  setConnected,
  setConnecting,
  setConnectionError,
  setPrimaryAccount,
  setInstallations,
  setAuthWindowOpen,
  resetGitHubState,
  updateLastConnectionCheck,
} = githubSlice.actions;

// Selectors with safe fallbacks
export const selectGitHubState = (state: RootState) => state.github || initialState;
export const selectIsGitHubConnected = (state: RootState) => state.github?.isConnected ?? false;
export const selectIsGitHubConnecting = (state: RootState) => state.github?.isConnecting ?? false;
export const selectGitHubPrimaryAccount = (state: RootState) => state.github?.primaryAccount ?? null;
export const selectGitHubInstallations = (state: RootState) => state.github?.installations ?? [];
export const selectGitHubConnectionError = (state: RootState) => state.github?.connectionError ?? null;
export const selectAuthWindowOpen = (state: RootState) => state.github?.authWindowOpen ?? false;
export const selectLastConnectionCheck = (state: RootState) => state.github?.lastConnectionCheck ?? 0;

// Computed selectors
export const selectPrimaryInstallation = (state: RootState) =>
  state.github?.installations?.find(installation => installation.isPrimary) ?? null;

export const selectShouldCheckConnection = (state: RootState) => {
  const now = Date.now();
  const lastCheck = state.github?.lastConnectionCheck ?? 0;
  const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds
  return now - lastCheck > fiveMinutes;
};

export default githubSlice.reducer;
