import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '@/lib/supabase';
import { config } from '@/config';

// Types for GitHub API responses
export interface GitHubInstallation {
  installation_id: string;
  account_login: string;
  account_type: string;
  user_github_login: string;
  app_slug: string;
  isPrimary?: boolean;
  account: {
    login: string;
    id: number;
    type: string;
  };
  target_type: string;
}

export interface GitHubRepository {
  id: string;
  repository_id: string;
  name: string;
  full_name: string;
  private: boolean;
  visibility: boolean;
  installation_id: string;
  permissions: {
    admin: boolean;
    push: boolean;
    pull: boolean;
    maintain: boolean;
    triage: boolean;
  };
}

export interface GitHubBranch {
  name: string;
  commit: {
    sha: string;
    url: string;
  };
  protected: boolean;
}

export interface GitHubUserDetails {
  id: string;
  github: {
    authorized: boolean;
    account_name: string;
  };
  github_installations: any[];
}

export interface CreateRepositoryRequest {
  installation_id: string;
  org?: string;
  name: string;
}

export interface PushToGitHubRequest {
  account_login: string;
  repo_name: string;
  branch_name: string;
  is_new_repo?: boolean;
  force?: boolean;
}

export interface PreviewURLResponse {
  preview_url: string;
  vscode_url: string;
  password: string;
  base_preview_url: string;
}

// Base query with authentication
const baseQuery = fetchBaseQuery({
  baseUrl: config.apiBaseUrl,
  prepareHeaders: async (headers) => {
    const session = await supabase.auth.getSession();
    if (session.data.session?.access_token) {
      headers.set('Authorization', `Bearer ${session.data.session.access_token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery,
  tagTypes: ['GitHubInstallations', 'GitHubRepositories', 'GitHubBranches', 'GitHubUserDetails', 'PreviewURL'],
  endpoints: (builder) => ({
    // GitHub User Details
    getGitHubUserDetails: builder.query<GitHubUserDetails, void>({
      query: () => '/user/details',
      providesTags: ['GitHubUserDetails'],
      transformResponse: (response: any) => response,
      transformErrorResponse: (response: any) => ({
        id: null,
        github: { authorized: false, account_name: '' },
        github_installations: [],
        error: response.data?.message || 'Failed to fetch user details'
      }),
    }),

    // GitHub Installations
    getGitHubInstallations: builder.query<GitHubInstallation[], void>({
      query: () => '/github/installations',
      providesTags: ['GitHubInstallations'],
      transformResponse: (response: any) => {
        if (response.error) {
          throw new Error(response.error);
        }

        // The API returns an array of installations directly
        const installations = Array.isArray(response) ? response : [];

        // Transform the data to match our expected structure
        // Note: isPrimary will be set by the component using userDetails
        return installations.map((installation: any) => ({
          installation_id: installation.account?.login || installation.account_login,
          account_login: installation.account?.login || installation.account_login,
          account_type: installation.target_type || installation.account_type,
          user_github_login: installation.account?.login || installation.user_github_login,
          app_slug: installation.app_slug,
          isPrimary: false, // Will be updated by component
          account: installation.account,
          target_type: installation.target_type,
        }));
      },
      transformErrorResponse: (response: any) => ({
        installations: [],
        error: response.data?.message || 'Failed to fetch installations'
      }),
    }),

    // GitHub Repositories
    getGitHubRepositories: builder.query<GitHubRepository[], string | void>({
      query: (accountLogin) => {
        const endpoint = accountLogin
          ? `/github/repositories?account_login=${encodeURIComponent(accountLogin)}`
          : '/github/repositories';
        return endpoint;
      },
      providesTags: (_result, _error, accountLogin) => [
        { type: 'GitHubRepositories', id: accountLogin || 'ALL' }
      ],
      transformResponse: (response: any, _meta, accountLogin) => {
        if (response.error) {
          throw new Error(response.error);
        }

        // The API returns { repositories: [...] }
        const repositories = response.repositories || [];

        // If we have an accountLogin filter, filter the repositories
        if (accountLogin) {
          return repositories.filter((repo: any) =>
            repo.owner?.login === accountLogin ||
            repo.full_name?.startsWith(`${accountLogin}/`)
          );
        }

        return repositories;
      },
      transformErrorResponse: (response: any) => ({
        repositories: [],
        error: response.data?.message || 'Failed to fetch repositories'
      }),
    }),

    // GitHub Branches
    getGitHubBranches: builder.query<GitHubBranch[], { accountLogin: string; repoName: string }>({
      query: ({ accountLogin, repoName }) => {
        const encodedAccountLogin = encodeURIComponent(accountLogin);
        const encodedRepoName = encodeURIComponent(repoName);
        return `/github/branches/${encodedAccountLogin}/${encodedRepoName}`;
      },
      providesTags: (_result, _error, { accountLogin, repoName }) => [
        { type: 'GitHubBranches', id: `${accountLogin}/${repoName}` }
      ],
      transformResponse: (response: any) => {
        if (response.error) {
          throw new Error(response.error);
        }
        return Array.isArray(response) ? response : 
               Array.isArray(response.branches) ? response.branches : [];
      },
      transformErrorResponse: (response: any) => ({
        branches: [],
        error: response.data?.message || 'Failed to fetch branches'
      }),
    }),

    // Create GitHub Repository
    createGitHubRepository: builder.mutation<any, CreateRepositoryRequest>({
      query: (data) => ({
        url: '/github/repositories',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['GitHubRepositories'],
    }),

    // Push to GitHub
    pushToGitHub: builder.mutation<any, { jobId: string; data: PushToGitHubRequest }>({
      query: ({ jobId, data }) => ({
        url: `/jobs/v0/push_to_github/${jobId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['GitHubRepositories', 'GitHubBranches'],
    }),

    // Save GitHub Installation
    saveGitHubInstallation: builder.mutation<any, { installationId: string; code: string }>({
      query: ({ installationId, code }) => ({
        url: '/github/installation',
        method: 'POST',
        body: {
          installation_id: installationId,
          code,
        },
      }),
      invalidatesTags: ['GitHubInstallations', 'GitHubUserDetails'],
    }),

    // Get Job Preview URL
    getJobPreviewUrl: builder.query<PreviewURLResponse, string>({
      query: (jobId) => `/jobs/v0/${jobId}/preview`,
      providesTags: (_result, _error, jobId) => [
        { type: 'PreviewURL', id: jobId }
      ],
      transformResponse: (response: PreviewURLResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to fetch preview URL'
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetGitHubUserDetailsQuery,
  useGetGitHubInstallationsQuery,
  useGetGitHubRepositoriesQuery,
  useGetGitHubBranchesQuery,
  useCreateGitHubRepositoryMutation,
  usePushToGitHubMutation,
  useSaveGitHubInstallationMutation,
  useGetJobPreviewUrlQuery,
  useLazyGetJobPreviewUrlQuery,
} = apiSlice;
