import { configureStore } from '@reduxjs/toolkit';
import containerReducer from './containerSlice';
import progressReducer from './progressSlice';
import configReducer from './configSlice';
import deployReducer from './deploySlice';
import githubReducer from './githubSlice';
import { apiSlice } from './api/apiSlice';

export const createStore = () => {
  return configureStore({
    reducer: {
      container: containerReducer,
      progress: progressReducer,
      config: configReducer,
      deploy: deployReducer,
      github: githubReducer,
      [apiSlice.reducerPath]: apiSlice.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        },
      }).concat(apiSlice.middleware),
  });
};
