// features/deploy/deploySlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { agentApi, DeploymentStep, DeploymentHistoryRun } from '@/services/agentApi';
import { toast } from '@/hooks/use-toast';
import { RootState } from '@/store';

export interface EnvValue {
  key: string;
  value: string;
  originalValue: string;
}

export interface EnvValues {
  id: string;
  key: string;
  value: string;
  originalValue: string;
}

// Custom domain types are defined inline

interface DeployState {
  deployStatus: "running" | "success" | "failed" | "loading" | "not_deployed";
  latestRunStatus: "running" | "success" | "failed" | "pending" | null;
  loading: boolean;
  deploymentSteps: DeploymentStep[];
  deploymentHistory: DeploymentHistoryRun[];
  currentStepIndex: number;
  deployUrl: string;
  customDomainUrl: string;
  runId: string;
  currentJobId: string | null;
  errorMessage: string | null;
  activeTabId: string;
  modifiedEnvs: EnvValues[];
  lastCheckedTimestamp: number;
  customDomain: {
    domain: string;
    status: "verified" | "pending" | "failed";
    dns_records: {
      type: string;
      name: string;
      value: string | undefined;
    }[];
  } | null;
  deployLogs: string[];
}

const initialState: DeployState = {
  deployStatus: 'not_deployed',
  latestRunStatus: null,
  loading: false,
  deploymentSteps: [],
  deploymentHistory: [],
  currentStepIndex: -1,
  deployUrl: '',
  customDomainUrl: '',
  runId: '',
  currentJobId: null,
  errorMessage: null,
  activeTabId: '',
  modifiedEnvs: [],
  lastCheckedTimestamp: 0,
  customDomain: null,
  deployLogs: []  
};

// Async Thunks
export const checkDeployStatus = createAsyncThunk(
  'deploy/checkDeployStatus',
  async ({ jobId, silent = false }: { jobId: string, silent?: boolean }, { getState, rejectWithValue }) => {
    if (!jobId) return rejectWithValue('No job ID provided');

    // Rate limiting check
    const state = getState() as RootState;
    const now = Date.now();
    const lastChecked = state.deploy.lastCheckedTimestamp;
    const timeSinceLastCheck = now - lastChecked;

    // ISSUE FIX: More intelligent rate limiting
    // Allow immediate checks after deployment starts or for non-silent user actions
    const MIN_CHECK_INTERVAL = 15000;
    const isRecentDeployment = state.deploy.deployStatus === 'running' && timeSinceLastCheck < 30000; // Within 30 seconds of deployment

    if (timeSinceLastCheck < MIN_CHECK_INTERVAL && silent && !isRecentDeployment) {
      //console.log(`[deploySlice] Rate limiting silent check - last check was ${timeSinceLastCheck}ms ago`);
      return rejectWithValue('Rate limited');
    }

    // For non-silent checks, allow more frequent calls but still have some protection
    if (timeSinceLastCheck < 1000 && !silent) {
      //console.log(`[deploySlice] Rate limiting non-silent check - too frequent (${timeSinceLastCheck}ms)`);
      return rejectWithValue('Rate limited');
    }

    try {
      // Get deployment status from API
      const response = await agentApi.getDeployStatus(jobId);

      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response format');
      }

      if (!response.status) {
        throw new Error('Missing status in response');
      }

      return { response, timestamp: now, prevStatus: state.deploy.deployStatus };
    } catch (error: any) {
      let errorMsg = "Failed to get deployment status";

      if (error.response && error.response.data) {
        if (error.response.data.detail) {
          errorMsg = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMsg = error.response.data.message;
        } else if (error.response.data.error) {
          errorMsg = error.response.data.error;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      // Show error toast for HTTP errors
      if (error.response && error.response.status >= 400 && !silent) {
        toast({
          title: "Deployment Error",
          description: errorMsg,
          variant: "destructive",
          duration: 2000,
        });
      }

      return rejectWithValue(errorMsg);
    }
  }
);

export const deployApp = createAsyncThunk(
  'deploy/deployApp',
  async ({ jobId, image, deployment_id, acknowledgements, db_mode }: { jobId: string, image?: string, deployment_id?: string, acknowledgements?: string, db_mode?: boolean }, { rejectWithValue }) => {
    if (!jobId) return rejectWithValue('No job ID provided');
    try {
      // Call API to deploy the app
      await agentApi.deployApp(jobId, image, deployment_id, acknowledgements, db_mode);
      return { jobId };
    } catch (error: any) {
      let errorMsg = "Failed to deploy application";

      if (error.response && error.response.data) {
        if (error.response.data.detail) {
          errorMsg = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMsg = error.response.data.message;
        } else if (error.response.data.error) {
          errorMsg = error.response.data.error;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }


      toast({
        title: "Deployment Error",
        description: errorMsg.includes("deploy in progress") ? "A deployment is currently in progress. Please wait for it to complete before starting a new one." : errorMsg.includes("max apps per user reached: 5 active apps") ? "You’ve reached the maximum limit of 5 active deployments. To deploy a new app, please shut down one of your existing deployments." : errorMsg,
        variant: "destructive",
        duration: 5000,
      });

      return rejectWithValue(errorMsg);
    }
  }
);

export const getDeployLogs = createAsyncThunk(
  'deploy/getDeployLogs',
  async (jobId: string, { rejectWithValue }) => {
    if (!jobId) return rejectWithValue('No job ID provided');

    try {
      const response = await agentApi.getDeployLogs(jobId);
      console.log("xoxo Logs Response:", response);

      // Merge all logs from different steps into a single array
      const allLogs: string[] = [];

      if (response) {
        // Add logs from each step type, filtering out empty strings
        if (response.build && Array.isArray(response.build)) {
          response.build.forEach(log => {
            if (log && log.trim()) {
              allLogs.push(log);
            }
          });
        }

        if (response.manage_secrets && Array.isArray(response.manage_secrets)) {
          response.manage_secrets.forEach(log => {
            if (log && log.trim()) {
              allLogs.push(log);
            }
          });
        }

        if (response.mongodb_migrate && Array.isArray(response.mongodb_migrate)) {
          response.mongodb_migrate.forEach(log => {
            if (log && log.trim()) {
              allLogs.push(log);
            }
          });
        }

        if (response.deploy && Array.isArray(response.deploy)) {
          response.deploy.forEach(log => {
            if (log && log.trim()) {
              allLogs.push(log);
            }
          });
        }

        if (response.health_check && Array.isArray(response.health_check)) {
          response.health_check.forEach(log => {
            if (log && log.trim()) {
              allLogs.push(log);
            }
          });
        }
      }

      return allLogs;
    } catch (error) {
      console.error("Error fetching deploy logs:", error);
      return rejectWithValue('Failed to fetch deploy logs');
    }
  }
);

export const loadDeploymentHistory = createAsyncThunk(
  'deploy/loadDeploymentHistory',
  async (jobId: string, { rejectWithValue }) => {
    if (!jobId) return rejectWithValue('No job ID provided');

    try {
      const response = await agentApi.getDeploymentHistory(jobId);
      if (response && response.runs && Array.isArray(response.runs)) {
        return response.runs;
      }
      return [];
    } catch (error) {
      console.error("Error loading deployment history:", error);
      return rejectWithValue('Failed to load deployment history');
    }
  }
);

export const shutDownDeployment = createAsyncThunk(
  'deploy/shutDownDeployment',
  async (jobId: string, { rejectWithValue }) => {
    if (!jobId) return rejectWithValue('No job ID provided');

    try {
      await agentApi.shutDownDeployment(jobId);
      return { jobId };
    } catch (error: any) {
      let errorMsg = "Failed to shut down deployment";

      if (error.response && error.response.data) {
        if (error.response.data.detail) {
          errorMsg = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMsg = error.response.data.message;
        } else if (error.response.data.error) {
          errorMsg = error.response.data.error;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      // Show error toast
      toast({
        title: "Shutdown Error",
        description: errorMsg,
        variant: "destructive",
        duration: 2000,
      });

      return rejectWithValue(errorMsg);
    }
  }
);

export const loadEnvironmentVariables = createAsyncThunk(
  'deploy/loadEnvironmentVariables',
  async (jobId: string, { getState, rejectWithValue }) => {
    if (!jobId) return rejectWithValue('No job ID provided');

    try {
      // First check if we have saved environment variables in the state
      const state = getState() as RootState;
      if (state.deploy.modifiedEnvs && state.deploy.modifiedEnvs.length > 0) {
        return state.deploy.modifiedEnvs;
      }

      // If no saved variables, fetch from API
      const response = await agentApi.getEnvs(jobId);

      // Transform the API response into the expected EnvValues[] format
      if (response && response.envs) {
        const transformedEnvs: EnvValues[] = Object.entries(response.envs).map(([key, value], index) => ({
          id: `env-${index}`,
          key,
          value: value,
          originalValue: value
        }));

        return transformedEnvs;
      }

      return [];
    } catch (error) {
      console.error("Error loading environment variables:", error);
      return rejectWithValue('Failed to load environment variables');
    }
  }
);

// Create the slice
const deploySlice = createSlice({
  name: 'deploy',
  initialState,
  reducers: {
    setActiveTabId: (state, action: PayloadAction<string>) => {
      state.activeTabId = action.payload;
    },
    saveEnvironmentVariables: (state, action: PayloadAction<EnvValues[]>) => {
      state.modifiedEnvs = action.payload;
    },
    resetDeployState: (state) => {
      state.deployStatus = 'not_deployed';
      state.latestRunStatus = null;
      state.deploymentSteps = [];
      state.currentStepIndex = -1;
      state.deployUrl = '';
      state.customDomainUrl = '';
      state.runId = '';
      state.errorMessage = null;
      state.customDomain = null;
    },
    resetDeploymentSteps: (state) => {
      // Only reset the steps and current step index, preserving other state
      state.deploymentSteps = [];
      state.currentStepIndex = -1;
    },
    setCurrentJobId: (state, action: PayloadAction<string | null>) => {
      state.currentJobId = action.payload;
    },
    setCustomDomain: (state, action: PayloadAction<{
      domain: string;
      status: "verified" | "pending" | "failed";
      dns_records: {
        type: string;
        name: string;
        value: string | undefined;
      }[];
    } | null>) => {
      state.customDomain = action.payload;
    }
  },
  extraReducers: (builder) => {
    // Handle checkDeployStatus
    builder.addCase(checkDeployStatus.pending, (state, action) => {
      if (!action.meta.arg.silent) {
        state.loading = true;
      }
    });
    builder.addCase(checkDeployStatus.fulfilled, (state, action) => {
      const { response, timestamp, prevStatus } = action.payload;

      // Update timestamp
      state.lastCheckedTimestamp = timestamp;

      // ISSUE FIX: Properly determine deployment status based on latest_run
      // If there's a latest_run with status 'running', the deployment should be 'running'
      // regardless of what the main status field says
      let actualDeployStatus = response.status;

      if (response.latest_run && response.latest_run.status === 'running') {
        //console.log(`[deploySlice] Latest run is running, setting deployStatus to 'running'`);
        actualDeployStatus = 'running';
      } else if (response.latest_run && response.latest_run.status && response.status === 'success') {
        // If there's a latest_run with a status and main status is success, use the latest_run status
        //console.log(`[deploySlice] Using latest_run status: ${response.latest_run.status}`);
        actualDeployStatus = response.latest_run.status;
      }

      // Update deployment status
      if (state.deployStatus !== actualDeployStatus) {
        //console.log(`[deploySlice] CHANGING deployStatus from '${state.deployStatus}' to '${actualDeployStatus}'`);
      }
      state.deployStatus = actualDeployStatus;

      // Always update latestRunStatus if available
      if (response.latest_run) {
        state.latestRunStatus = response.latest_run.status;
      }

      // Update deploy URL if available
      if (response.deploy_url) {
        state.deployUrl = response.deploy_url;
      }

      // Update custom domain information if available
      if (response.custom_domain) {
        // Check if the custom_domain has empty values, which indicates no records
        if (response.custom_domain.domain === "" ||
            !response.custom_domain.status ||
            !response.custom_domain.dns_records) {
          state.customDomain = null;
          state.customDomainUrl = '';
        } else {
          state.customDomain = {
            domain: response.custom_domain.domain,
            status: response.custom_domain.status,
            dns_records: Array.isArray(response.custom_domain.dns_records)
              ? response.custom_domain.dns_records
              : [response.custom_domain.dns_records]
          };

          // If custom domain exists and status is verified, update the customDomainUrl
          if (response.custom_domain.status === 'verified') {
            // Format the domain with https:// prefix if not already present
            const domain = response.custom_domain.domain;
            state.customDomainUrl = domain.startsWith('http') ? domain : `https://${domain}`;
          } else {
            state.customDomainUrl = '';
          }
        }
      } else {
        state.customDomain = null;
        state.customDomainUrl = '';
      }

      // Update run ID and latest run status
      if (response.latest_run && response.latest_run.run_id && response.latest_run.status === 'success') {
        state.runId = response.latest_run.run_id;
        if (response.latest_run.status) {
          state.latestRunStatus = response.latest_run.status;
        }
      } else if (response.deployed_run_id) {
        state.runId = response.deployed_run_id;
      }


      // Update deployment steps and current step index
      if (response.latest_run && response.latest_run.steps && Array.isArray(response.latest_run.steps)) {
        // Deep compare to avoid unnecessary re-renders if steps haven't changed
        const stepsChanged = JSON.stringify(state.deploymentSteps) !== JSON.stringify(response.latest_run.steps);
        if (stepsChanged) {
          //console.log(`[deploySlice] Deployment steps changed, updating state`);
          state.deploymentSteps = response.latest_run.steps;
        }

        // Determine current step index
        let currentIndex = -1;

        if (response.status === "running" && response.latest_run.steps.every((step: DeploymentStep) => step.status === "pending")) {
          currentIndex = 0;
        } else {
          const runningStepIndex = response.latest_run.steps.findIndex((step: DeploymentStep) => step.status === "running");
          if (runningStepIndex !== -1) {
            currentIndex = runningStepIndex;
          } else {
            for (let i = 0; i < response.latest_run.steps.length - 1; i++) {
              if (response.latest_run.steps[i].status === "success" && response.latest_run.steps[i + 1].status === "pending") {
                currentIndex = i + 1;
                break;
              }
            }

            if (currentIndex === -1) {
              const pendingStepIndex = response.latest_run.steps.findIndex((step: DeploymentStep) => step.status === "pending");
              if (pendingStepIndex !== -1) {
                currentIndex = pendingStepIndex;
              } else if (response.status === "success") {
                currentIndex = response.latest_run.steps.length - 1;
              }
            }
          }
        }

        state.currentStepIndex = currentIndex;
      }

      // Clear error message and loading state
      state.errorMessage = null;
      state.loading = false;
    });
    builder.addCase(checkDeployStatus.rejected, (state, action) => {
      // Only update state if the rejection is not due to rate limiting
      if (action.payload !== 'Rate limited') {
        state.errorMessage = action.payload as string;
        state.deployStatus = 'not_deployed';
        state.latestRunStatus = null;
        state.deploymentSteps = [];
        state.currentStepIndex = -1;
        state.loading = false;
      }
    });

    // Handle deployApp
    builder.addCase(deployApp.pending, (state) => {
      //console.log(`[deploySlice] deployApp.pending`);
      state.loading = true;
      state.errorMessage = null;
    });
    builder.addCase(deployApp.fulfilled, (state, action) => {
      //console.log(`[deploySlice] deployApp.fulfilled:`, action.payload);
      //console.log(`[deploySlice] Setting deployStatus to 'running' after successful deployApp call`);
      state.deployStatus = 'running';
      state.latestRunStatus = 'running';
      state.currentJobId = action.payload.jobId;
      state.loading = false;

      // Clear deployment steps when starting a new deployment
      // This prevents showing old steps during the transition
      state.deploymentSteps = [];
      state.currentStepIndex = -1;

    });
    builder.addCase(deployApp.rejected, (state, action) => {
      //console.log(`[deploySlice] deployApp.rejected:`, action.payload);
      state.errorMessage = action.payload as string;
      state.deployStatus = 'not_deployed';
      state.latestRunStatus = null;
      state.loading = false;
    });

    // Handle loadDeploymentHistory
    builder.addCase(loadDeploymentHistory.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(loadDeploymentHistory.fulfilled, (state, action) => {
      state.deploymentHistory = action.payload;
      state.loading = false;
    });
    builder.addCase(loadDeploymentHistory.rejected, (state) => {
      state.loading = false;
    });

    // Handle shutDownDeployment
    builder.addCase(shutDownDeployment.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(shutDownDeployment.fulfilled, (state) => {
      state.deployStatus = 'not_deployed';
      state.latestRunStatus = null;
      state.loading = false;
      // Clear deployment history when shutting down
      state.deploymentHistory = [];
      // Clear other deployment-related state
      state.deployUrl = '';
      state.customDomainUrl = '';
      state.runId = '';
      state.deploymentSteps = [];
      state.currentStepIndex = -1;
    });
    builder.addCase(shutDownDeployment.rejected, (state) => {
      state.loading = false;
    });

    // Handle getDeployLogs
    builder.addCase(getDeployLogs.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(getDeployLogs.fulfilled, (state, action) => {
      state.deployLogs = action.payload;
      state.loading = false;
    });
    builder.addCase(getDeployLogs.rejected, (state) => {
      state.loading = false;
    });

    // Handle loadEnvironmentVariables
    builder.addCase(loadEnvironmentVariables.fulfilled, (state, action) => {
      state.modifiedEnvs = action.payload;
    });
  }
});

// Export actions and reducer
export const {
  setActiveTabId,
  saveEnvironmentVariables,
  resetDeployState,
  resetDeploymentSteps,
  setCurrentJobId,
  setCustomDomain
} = deploySlice.actions;

export default deploySlice.reducer;

// Selectors
export const selectDeployStatus = (state: RootState) => state.deploy.deployStatus;
export const selectLatestRunStatus = (state: RootState) => state.deploy.latestRunStatus;
export const selectLoading = (state: RootState) => state.deploy.loading;
export const selectDeploymentSteps = (state: RootState) => state.deploy.deploymentSteps;
export const selectDeploymentHistory = (state: RootState) => state.deploy.deploymentHistory;
export const selectCurrentStepIndex = (state: RootState) => state.deploy.currentStepIndex;
export const selectDeployUrl = (state: RootState) => state.deploy.deployUrl;
export const selectCustomDomainUrl = (state: RootState) => state.deploy.customDomainUrl;
export const selectRunId = (state: RootState) => state.deploy.runId;
export const selectCurrentJobId = (state: RootState) => state.deploy.currentJobId;
export const selectErrorMessage = (state: RootState) => state.deploy.errorMessage;
export const selectActiveTabId = (state: RootState) => state.deploy.activeTabId;
export const selectModifiedEnvs = (state: RootState) => state.deploy.modifiedEnvs;
export const selectCustomDomain = (state: RootState) => state.deploy.customDomain;
export const selectDeployLogs = (state: RootState) => state.deploy.deployLogs;
