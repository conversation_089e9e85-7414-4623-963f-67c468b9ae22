import { useEffect, useState } from "react";
import SparkleSV<PERSON> from "@/assets/oauth/sparkle.svg";
import EnvelopeSVG from "@/assets/oauth/envelope-default.svg";
import NftSVG from "@/assets/oauth/nft-default.svg";
import LayerSVG from "@/assets/oauth/layer-three.svg";
import BGImage from "@/assets/oauth/image 4.png";
import GoogleSVG from "@/assets/oauth/google.svg";
import ShieldSVG from "@/assets/oauth/foundation_shield.svg";
import Emergent from "@/assets/oauth/emergent.svg";
import { config } from "@/config";
import UpArrow from "@/components/icons/UpArrow";
import LinkSVG from "@/assets/panels/link.svg"
import LinkIcon from "@/components/icons/LinkIcon";

interface CardInfo {
  title: string;
  image: string;
}

const DATA: CardInfo[] = [
  
  
  {
    title: "Securely verify your identity",
    image: SparkleSVG,
  },
  {
    title: "Retrieve your email information",
    image: EnvelopeSVG,
  },
  {
    title: "Access your basic profile details",
    image: NftSVG,
  },
  {
    title: "Maintain your login sessions",
    image: LayerSVG,
  },
];


const useIsInIframe = () => {
  const [isInIframe, setIsInIframe] = useState(false);

  useEffect(() => {
    const checkIfInIframe = () => {
      try {
        return window.self !== window.top;
      } catch (e) {
        return true;
      }
    };

    setIsInIframe(checkIfInIframe());
  }, []);

  return isInIframe;
};

export default function OAuth() {
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  // const [status, setStatus] = useState<'initial' | 'processing' | 'success' | 'error'>('initial');
  const [message, setMessage] = useState<string>('');
  const isInIframe = useIsInIframe();

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const redirectParam = urlParams.get('redirect');

    if (!redirectParam) {
      console.error('OAuth: No redirect URL provided');
      setError('No redirect URL provided. Authentication cannot proceed.');
      return;
    }

    console.log('OAuth: Got redirect URL from params:', redirectParam);
    setRedirectUrl(redirectParam);
  }, []);



  const handleGoogleAuth = () => {
    if (!redirectUrl) {
      console.error('OAuth: No redirect URL available');
      setError('No redirect URL available. Please refresh and try again.');
      return;
    }
    const backendUrl = `${config.apiBaseUrl}/auth/v1/env/oauth?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = backendUrl;
  };

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="text-center">
          <h2 className="mb-4 text-xl font-medium text-red-600">Authentication Error</h2>
          <p className="mb-4 text-gray-600">{error}</p>
          <button
            type="button"
            onClick={() => window.close()}
            className="px-4 py-2 text-white bg-black rounded hover:bg-[#333333]"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex lg:flex-row flex-col w-full min-h-screen max-h-screen Z-[999]">
        <div className="relative flex flex-col justify-end flex-[44] lg:h-[100vh] lg:px-[56px] lg:pb-[56px]">
          <img
            src={BGImage}
            alt="Background"
            className="absolute top-0 left-0 object-cover w-full h-full"
          />
          <span className="z-10 p-6 pb-5 text-[18px] font-semibold">
            This app will be able to :
          </span>
          <div className="flex flex-row gap-5 px-6 pb-8 overflow-y-auto lg:grid lg:grid-cols-2">
            {DATA.map((item, index) => (
              <div
                key={index}
                className="flex flex-col max-lg:min-h-[140px] max-lg:min-w-[200px] rounded-[8px] shadow-white backdrop-blur-[50px] p-5 pt-6 gap-[34px]"
              >
                <img src={item.image} alt={item.title} className="w-6 h-6" />
                <p className="leading-[24px] tracking-[-0.2px] text-[#FFFFFFCC] font-semibold text-[18px]">
                  {item.title}
                </p>
              </div>
            ))}
          </div>
        </div>
        <div className="flex-[66] lg:h-[100vh] max-lg:justify-between p-6 pb-[40px] flex flex-col lg:p-[40px] h-full bg-[#FFFFFF]">
          <div className="flex flex-col items-center justify-center w-full h-full">
            <div className="flex max-lg:mt-[40px] gap-[40px] items-center flex-col justify-center w-[500px]">
              <span className="font-nothing tracking-[0.04em] uppercase text-black">
                {isInIframe ? "Open in a new tab to continue to Login" : "Log In to your app"}
              </span>

              {!isInIframe && (
                <button
                  type="button"
                  title="Google Signin"
                  onClick={handleGoogleAuth}
                  className="rounded-full p-[18px] gap-4 w-full bg-black flex lg:min-w-[400px] hover:bg-[#333333]"
                >
                  <img src={GoogleSVG} alt="Google" className="w-6 h-6" />
                  <div className="flex justify-center w-full">
                    <span className="font-medium text-[16px] tracking-[-0.25px]">
                      Continue with Google
                    </span>
                  </div>
                </button>
              )}

              {isInIframe && (
                <div className="flex flex-row w-full flex-1 space-x-3 gap-2">
                  <button
                    type="button"
                    title="Google Signin"
                    onClick={() => {
                      window.location.href = redirectUrl || "/";
                    }}
                    className="rounded-full p-[18px] gap-4 w-full bg-white border-[2px] border-black text-black flex lg:min-w-[400px]"
                  >
                    <div className="flex justify-center w-full items-center gap-2">
                      <UpArrow color="#000000" direction="left" />
                      <span className="font-medium text-[16px] tracking-[-0.25px]">
                        Back
                      </span>
                    </div>
                  </button>
                  <button
                    type="button"
                    title="Google Signin"
                    onClick={() => {
                      // Instead of opening from iframe, ask parent to open the tab
                      const currentUrl = window.location.href;
                      window.open(currentUrl, "_blank" );
                    }}
                    className="rounded-full p-[18px] gap-4 w-full bg-background text-white border-background border-[2px] flex lg:min-w-[400px]"
                  >
                    <div className="flex justify-center w-full items-center gap-2">
                      <span className="font-medium text-[16px] tracking-[-0.25px]">
                        Open in new tab
                      </span>
                      <LinkIcon color="#fff" />
                    </div>
                  </button>
                </div>
              )}

              {!isInIframe && (
                <p className="text-[#45474D] opacity-50 text-[11px] font-['Inter'] text-center leading-[20px] mt-6">
                  By continuing, you agree to emergent{" "}
                  <a
                    href="https://app.emergent.sh/terms-of-service"
                    target="_blank"
                    rel="noreferrer noopener"
                    className="font-medium text-black underline"
                  >
                    Terms of Service
                  </a>{" "}
                  and{" "}
                  <a
                    href="/privacy-policy"
                    target="_blank"
                    rel="noreferrer noopener"
                    className="font-medium text-black underline"
                  >
                    Privacy Policy
                  </a>
                  . This site is protected by reCAPTCHA Enterprise and the
                  Google{" "}
                  <a
                    href="https://app.emergent.sh/privacy-policy"
                    target="_blank"
                    rel="noreferrer noopener"
                    className="font-medium text-black underline"
                  >
                    Privacy Policy
                  </a>{" "}
                  and{" "}
                  <a
                    href="https://app.emergent.sh/terms-of-service"
                    target="_blank"
                    rel="noreferrer noopener"
                    className="font-medium text-black underline"
                  >
                    Terms of service
                  </a>{" "}
                  apply.
                </p>
              )}

              {isInIframe && (
                <p className="text-[#45474D] opacity-50 text-base font-['Inter'] text-center leading-[20px] mt-6">
                  Login does't work in an iframe. Please open in a new tab.
                </p>
              )}
            </div>
          </div>

          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-[6px]">
              <img src={ShieldSVG} alt="Shield" className="" />
              <span className="text-[#04B200] tracking-[0.04em] text-[12px] font-nothing uppercase">
                Log In secured by
              </span>
            </div>
            <a
              href="https://emergent.sh"
              target="_blank"
              rel="noreferrer noopener"
            >
              <img src={Emergent} alt="Emergent" className="h-6 " />
            </a>
          </div>
        </div>
      </div>
    </>
  );
}


