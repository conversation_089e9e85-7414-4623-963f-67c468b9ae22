{"name": "emergent", "private": true, "version": "1.0.21", "main": ".vite/build/index.js", "workspaces": ["src/lib/*"], "scripts": {"start": "yarn build:cli && electron-forge start", "start:web": "vite", "package": "yarn build:cli && electron-forge package", "make": "yarn build:cli && electron-forge make", "publish": "yarn build:cli && electron-forge publish", "build:web": "vite build", "lint": "eslint --ext .ts,.tsx .", "build": "yarn build:cli && yarn workspaces run build && vite build", "build:cli": "yarn workspace @emergent/neon build", "cli": "yarn workspace @emergent/neon cli", "postinstall": "yarn build:cli"}, "dependencies": {"@electron-forge/maker-dmg": "^7.6.1", "@electron-toolkit/utils": "^3.0.0", "@electron/remote": "^2.1.2", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/themes": "^3.1.6", "@reduxjs/toolkit": "^2.0.1", "@sentry/electron": "^5.9.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.47.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash.debounce": "^4.0.9", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "add": "^2.0.6", "ai": "^4.0.33", "autoprefixer": "^10.4.16", "axios": "^1.7.9", "blitzllama-js": "^1.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "dotenv": "^16.4.7", "electron-squirrel-startup": "^1.0.1", "framer-motion": "^12.7.1", "ldrs": "^1.1.7", "lodash.debounce": "^4.0.8", "logrocket": "^9.0.2", "lottie-react": "^2.4.1", "lucide-react": "^0.469.0", "monaco-editor": "^0.52.2", "motion": "12.5.0", "postcss": "^8.4.32", "posthog-js": "^1.236.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.3", "react-redux": "^9.0.4", "react-resizable-panels": "^3.0.2", "react-router": "^7.1.1", "react-router-dom": "^7.1.1", "react-virtualized-auto-sizer": "^1.0.25", "react-virtuoso": "^4.12.3", "react-window": "^1.8.11", "redact-pii": "^3.4.0", "redux-persist": "^6.0.0", "sonner": "^2.0.5", "string-argv": "^0.3.2", "swiper": "^11.2.8", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.0", "uuid": "^11.0.5", "vaul": "^1.1.2", "yarn": "^1.22.22", "zod": "^3.24.1"}, "devDependencies": {"@electron-forge/cli": "^7.6.0", "@electron-forge/maker-deb": "^7.6.0", "@electron-forge/maker-rpm": "^7.6.0", "@electron-forge/maker-squirrel": "^7.6.0", "@electron-forge/maker-zip": "^7.6.0", "@electron-forge/plugin-auto-unpack-natives": "^7.6.0", "@electron-forge/plugin-fuses": "^7.6.0", "@electron-forge/plugin-vite": "^7.6.0", "@electron/fuses": "^1.8.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/redux-persist": "^4.3.1", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vitejs/plugin-react": "^4.2.1", "electron": "^33.2.1", "eslint": "^8.55.0", "eslint-plugin-import": "^2.25.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.0.0", "typescript": "~4.5.4", "vite": "^6.0.7", "vite-plugin-svgr": "^4.3.0"}}